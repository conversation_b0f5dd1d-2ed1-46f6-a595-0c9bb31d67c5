<?php

use App\MCP\Tools\ArticleContinuationTool;
use App\MCP\Tools\EssayCorrectionTool;
use App\MCP\Tools\FetchWebUrlTool;
use App\MCP\Tools\FoodOrCalorieTool;
use App\MCP\Tools\GenImageTool;
use App\MCP\Tools\GenPPTOutLineTool;
use App\MCP\Tools\GetCurrentWeatherTool;
use App\MCP\Tools\GetSearchMobileTool;
use App\MCP\Tools\GetWebSearchTool;
use App\MCP\Tools\HistoricalStorytellingSessionTool;
use App\MCP\Tools\MasterOfRomanceTool;
use App\MCP\Tools\MovieCurrentlyBeingReleasedTool;
use App\MCP\Tools\OrcWritingEraseTool;
use App\MCP\Tools\QueryBankCardTool;
use App\MCP\Tools\QueryBilibiliHotTool;
use App\MCP\Tools\QueryCarSeriesTool;
use App\MCP\Tools\QueryCommonlyDomesticTelephoneTool;
use App\MCP\Tools\QueryHotNewsTool;
use App\MCP\Tools\QueryLotteryInfoTool;
use App\MCP\Tools\QueryLotteryPeriodNumberTool;
use App\MCP\Tools\QueryPersonFaceTool;
use App\MCP\Tools\QueryScenicAreaTool;
use App\MCP\Tools\QuerySecondHandCarTool;
use App\MCP\Tools\QueryShipmentTool;
use App\MCP\Tools\SearchForNovelListTool;
use App\MCP\Tools\SearchHotFormDouyinTool;
use App\MCP\Tools\SearchJobTool;
use App\MCP\Tools\SearchManManBuyTool;
use App\MCP\Tools\SearchTrainTool;
use App\MCP\Tools\SearchVideoFormBilibiliTool;
use App\MCP\Tools\SetAlarmClockTool;
use App\MCP\Tools\StoreSearchSuggestionTool;
use App\MCP\Tools\TextPolishingTool;
use App\MCP\Tools\TodayOnHistoryTool;
use App\MCP\Tools\WebPageScreenshotTool;
use App\MCP\Tools\WebSearchTool;

return [
    /*
    |--------------------------------------------------------------------------
    | MCP Server Activation
    |--------------------------------------------------------------------------
    |
    | Enable or disable the MCP server functionality. When disabled, no routes
    | will be registered and the server will not respond to any requests.
    |
    */
    'enabled'         => env('MCP_SERVER_ENABLED', true),
    'stub_path'       => app_path('MCP/Stubs/tool.stub'),
    /*
    |--------------------------------------------------------------------------
    | Server Information
    |--------------------------------------------------------------------------
    |
    | Configuration for the MCPServer instance. These values are used when
    | registering the MCPServer as a singleton in the service container.
    |
    */
    'server'          => [
        'name'    => 'Water Mcp Server',
        'version' => '1.3.5',
    ],

    /*
   |--------------------------------------------------------------------------
   | Transport Provider Configuration
   |--------------------------------------------------------------------------
   |
   | The transport provider determines how the MCP server communicates with clients.
   |
   | Available providers:
   | - 'streamable_http' (recommended): Standard HTTP requests, works everywhere
   | - 'sse' (legacy, deprecated): Server-Sent Events with pub/sub, requires specific setup
   |
   | Note: SSE provider requires Laravel Octane or similar for concurrent connections
   |
   */
    'default_path'    => 'water_mcp',
    //    'server_provider' => 'streamable_http',
    'server_provider' => 'sse',
    'domain'          => null,

    /*
    |--------------------------------------------------------------------------
    | SSE Route Middleware
    |--------------------------------------------------------------------------
    |
    | Middleware to apply to the SSE route (/{default_path}/sse). Use this to protect
    | your SSE endpoint with authentication or other middleware as needed.
    | This will only be applied to the SSE endpoint, not to the message endpoint.
    |
    */
    'middlewares'     => [
        // 'auth:api'
    ],

    /*
    |--------------------------------------------------------------------------
    | SSE Adapters Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the different SSE adapters available in the MCP server.
    | Each adapter has its own configuration options.
    |
    | Adapters function as a pub/sub message broker between clients and the server.
    | When a client sends a message to the server endpoint, the server processes it
    | and publishes a response through the adapter. SSE connections subscribe to
    | these messages and deliver them to the client in real-time.
    |
    | The Redis adapter uses Redis lists as message queues, with each client having
    | its own queue identified by a unique client ID. This enables efficient and
    | scalable real-time communication in distributed environments.
    |
    */
    'sse_adapter'     => 'redis',
    'adapters'        => [
        'redis' => [
            'prefix'     => 'mcp_sse_',
            'connection' => env('MCP_REDIS_CONNECTION', 'default'), // database.php redis
            'ttl'        => 100,
        ],
        // Add more adapter configurations as needed
    ],

    /*
    |--------------------------------------------------------------------------
    | Tools List
    | https://modelcontextprotocol.io/docs/concepts/tools
    |--------------------------------------------------------------------------
    |
    | List of tools supported by the MCP server. These values are used when
    | generating the tool list for the client.
    |
    */
    // Register your tools here
    'tools'           => [
        GenImageTool::class,
        QueryShipmentTool::class,
        GenPPTOutLineTool::class,
        QueryBankCardTool::class,
        QueryPersonFaceTool::class,
        QueryScenicAreaTool::class,
        EssayCorrectionTool::class,
        TextPolishingTool::class,
        OrcWritingEraseTool::class,
        ArticleContinuationTool::class,
        //        GenPostImageTool::class,
        MovieCurrentlyBeingReleasedTool::class,
        QueryBilibiliHotTool::class,
        SearchVideoFormBilibiliTool::class,
        TodayOnHistoryTool::class,
        SearchForNovelListTool::class,
        FetchWebUrlTool::class,
        SearchHotFormDouyinTool::class,
        FoodOrCalorieTool::class,
        StoreSearchSuggestionTool::class,
        SearchTrainTool::class,
        QueryLotteryInfoTool::class,
        QueryLotteryPeriodNumberTool::class,
        QueryCommonlyDomesticTelephoneTool::class,
        MasterOfRomanceTool::class,
        SearchManManBuyTool::class,
        WebPageScreenshotTool::class,
        //        \App\MCP\Tools\SendToMailTool::class,
        HistoricalStorytellingSessionTool::class,
        WebSearchTool::class,
        QueryCarSeriesTool::class,
        QuerySecondHandCarTool::class,
        SearchJobTool::class,
        SetAlarmClockTool::class,
        \App\MCP\Tools\OfflineTaskTool::class,
    ],

    'voice-tools' => [
        QueryShipmentTool::class,
        GetCurrentWeatherTool::class,
        GetSearchMobileTool::class,
        MovieCurrentlyBeingReleasedTool::class,
        GetWebSearchTool::class,
    ],

    'resources'          => [],
    'resource_templates' => [],
    'prompts'            => [],
];
