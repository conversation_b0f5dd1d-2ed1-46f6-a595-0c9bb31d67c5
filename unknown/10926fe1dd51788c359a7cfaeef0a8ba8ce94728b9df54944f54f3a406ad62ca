<?php

namespace Modules\Payment\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Payment\Models\Bank;

class BankTableSeeder extends Seeder
{
    public function run(): void
    {
        $seeders = [
            [
                'name'  => '中国银行',
                'short' => 'BOC',
                'order' => 1,
            ],
            [
                'name'  => '建设银行',
                'short' => 'CCB',
                'order' => 2,
            ],
            [
                'name'  => '农业银行',
                'short' => 'ABC',
                'order' => 3,
            ],
            [
                'name'  => '工商银行',
                'short' => 'ICBC',
                'order' => 4,
            ],
            [
                'name'  => '交通银行',
                'short' => 'BCM',
                'order' => 5,
            ],
            [
                'name'  => '邮政储蓄',
                'short' => 'PSBC',
                'order' => 6,
            ],
            [
                'name'  => '招商银行',
                'short' => 'CMB',
                'order' => 7,
            ],
            [
                'name'  => '中信银行',
                'short' => 'CITIC',
                'order' => 8,
            ],
            [
                'name'  => '浦发银行',
                'short' => 'SPDB',
                'order' => 9,
            ],
            [
                'name'  => '兴业银行',
                'short' => 'CIB',
                'order' => 10,
            ],
            [
                'name'  => '民生银行',
                'short' => 'CMBC',
                'order' => 11,
            ],
            [
                'name'  => '中国光大银行',
                'short' => 'CEB',
                'order' => 12,
            ],
            [
                'name'  => '平安银行',
                'short' => 'PAB',
                'order' => 13,
            ],
            [
                'name'  => '华夏银行',
                'short' => 'HXB',
                'order' => 14,
            ],
            [
                'name'  => '广发银行',
                'short' => 'CGB',
                'order' => 15,
            ],
        ];

        foreach ($seeders as $seeder) {
            Bank::create($seeder);
        }
    }
}
