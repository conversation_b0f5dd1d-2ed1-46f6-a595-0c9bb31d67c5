<?php

namespace Modules\Payment\Listeners;

use App\Events\UserCreatedEvent;
use App\Models\Module;
use App\Models\SystemConfig;
use Modules\Payment\Models\Account;

class UserCreatedListener
{
    public function handle(UserCreatedEvent $event): void
    {
        $user = $event->user;

        $account = Account::create([
            'user' => $user,
        ]);

        if (Module::isEnabled('Supply')) {
            $user->security()->create([
                'password' => md5(uniqid().config('app.key')),
            ]);
        }
        $registerScore = SystemConfig::getValue('RegisterScore', 0);
        if ($registerScore > 0) {
            $account->exec(
                accountRule: 'system_score',
                amount: $registerScore,
                source: [
                    'remark' => '注册赠送积分',
                ],
            );
        }
    }
}