<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_accounts', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->decimal('balance', 20)
                ->default(0)
                ->comment('主账户余额，一般来替代现金');
            $table->decimal('score', 20)
                ->default(0)
                ->comment('积分余额');
            $table->decimal('draw', 20)
                ->default(0)
                ->comment('其他代币');
            $table->decimal('video', 20)
                ->default(0)
                ->comment('预留账户1');
            $table->decimal('audio', 20)
                ->default(0)
                ->comment('预留账户2');
            $table->timestamps();
            $table->softDeletes()
                ->index();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_accounts');
    }
};
