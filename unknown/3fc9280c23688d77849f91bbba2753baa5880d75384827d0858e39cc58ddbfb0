<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Enums\BalancePayStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_balances', function (Blueprint $table) {
            $table->id();
            $table->string('no', 32)
                ->unique();
            $table->unsignedBigInteger('payment_id')->index();
            $table->unsignedBigInteger('account_id')->index();
            $table->enum('channel', AccountType::values())
                ->index()
                ->comment('账户渠道');
            $table->decimal('amount', 20);
            $table->enum('status', BalancePayStatus::values())
                ->default(BalancePayStatus::UNPAY->value);
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_balances');
    }
};
