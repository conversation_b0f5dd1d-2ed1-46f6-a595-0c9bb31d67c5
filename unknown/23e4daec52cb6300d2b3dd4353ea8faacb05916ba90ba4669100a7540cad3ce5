<?php

namespace Modules\Storage\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Storage\Models\Upload;

class UploadController extends AdminController
{
    protected string $title = '文件存储';

    public function grid(): Grid
    {
        return Grid::make(Upload::with('user')->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->column('上传用户')
                ->display(function () {
                    if ($this->user) {
                        return $this->user->showName;
                    } else {
                        return '';
                    }
                });
            $grid->column('hash')
                ->copyable();
            $grid->column('size', '文件大小')
                ->filesize();
            $grid->column('type', '文件类型');
            $grid->column('disk', '磁盘');
            $grid->column('path', '路径')
                ->copyable();
            $grid->column('created_at');
        });
    }
}