<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use Exception;
use GuzzleHttp\Psr7\Response;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Exceptions\AccountException;
use Modules\Payment\Exceptions\InsufficientFunds;
use Modules\Payment\Exceptions\RuleException;
use Modules\Payment\Models\Account;
use Modules\Payment\Models\Combine;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;

class BalanceAdapter implements PaymentAdapter
{
    protected Account $driver;

    /**
     * @throws \Exception
     */
    public function __construct(protected Payment|Combine|null $payment)
    {
        if (Account::where('user_id', $this->payment->user_id)->exists()) {
            $this->driver = Account::where('user_id', $this->payment->user_id)->first();
        } else {
            throw new AccountException('缺少账户信息');
        }

//        if (is_null($this->payment?->user?->security?->password)) {
//            throw new AccountException('未设置支付密码');
//        }
    }

    public function getConfig(): array
    {
        return [
            'notify_url' => route('api.payment.notify', Gateway::BALANCE->value),
        ];
    }

    /**
     * Notes   : 创建支付订单
     *
     * @Date   : 2023/5/19 15:16
     * <AUTHOR> <Jason.C>
     * @param  string  $channel
     * @return array
     * @throws \Modules\Payment\Exceptions\InsufficientFunds
     */
    private function createOrder(string $channel): array
    {
        $channel = AccountType::tryFrom($channel);

        if ($channel != AccountType::BALANCE && $this->payment->amount > $this->driver->{$channel->value}) {
            # TODO ERP金额判断
            throw new InsufficientFunds($channel->toString().'x');
        }
        if ($this->payment->getMorphClass() == Combine::class) {
            foreach ($this->payment->payments as $payment) {
                $payment->balances()->create([
                    'account_id' => $this->driver->getKey(),
                    'amount'     => $payment->amount,
                    'channel'    => $channel,
                ]);
            }
            return [
                'pay_no'     => $this->payment->no,
                'payment_no' => $this->payment->no,
                'amount'     => $this->payment->amount,
                'channel'    => $channel,
            ];
        } else {
            $bp = $this->payment->balances()->create([
                'account_id' => $this->driver->getKey(),
                'amount'     => $this->payment->amount,
                'channel'    => $channel,
            ]);

            return [
                'pay_no'     => $bp->no,
                'payment_no' => $this->payment->no,
                'amount'     => $this->payment->amount,
                'channel'    => $channel,
            ];
        }
    }

    /**
     * Notes   : 使用余额支付
     *
     * @Date   : 2023/5/19 15:17
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Modules\Payment\Exceptions\InsufficientFunds
     */
    public function balance(): array
    {
        return $this->createOrder(__FUNCTION__);
    }

    /**
     * Notes   : 积分支付
     *
     * @Date   : 2023/5/19 15:17
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Modules\Payment\Exceptions\InsufficientFunds
     */
    public function score(): array
    {
        return $this->createOrder(__FUNCTION__);
    }

    /**
     * Notes   : 代币支付
     *
     * @Date   : 2023/5/19 15:17
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Modules\Payment\Exceptions\InsufficientFunds
     */
    public function coins(): array
    {
        return $this->createOrder(__FUNCTION__);
    }

    /**
     * Notes   : 现金支付
     *
     * @Date   : 2023/5/19 15:17
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Modules\Payment\Exceptions\InsufficientFunds
     */
    public function cash(): array
    {
        return $this->createOrder(__FUNCTION__);
    }

    /**
     * Notes   : 其他的支付
     *
     * @Date   : 2023/5/19 15:17
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Modules\Payment\Exceptions\InsufficientFunds
     */
    public function other(): array
    {
        return $this->createOrder(__FUNCTION__);
    }

    /**
     * Notes   : 回调
     *
     * @Date   : 2023/5/19 15:17
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function callback(): array
    {
        return [];
    }

    /**
     * Notes   : 成功的返回
     *
     * @Date   : 2023/5/19 15:17
     * <AUTHOR> <Jason.C>
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function success(): ResponseInterface
    {
        return new Response(
            200,
            ['Content-Type' => 'application/json'],
            json_encode(['code' => 'SUCCESS', 'message' => lecho("Success")]),
        );
    }

    /**
     * Notes   : 查询支付结果
     *
     * @Date   : 2023/5/19 15:17
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function query(): array
    {
        return [];
    }

    public function refund(string $refundNo, float $refundAmount, string $desc = null): array
    {
        try {
            $rule = 'refund_'.explode('_', $this->payment->channel->value)[1];
            $res  = $this->payment->user->account->exec(
                accountRule: $rule,
                amount: $refundAmount,
                source: [
                    'refund_no' => $refundNo,
                ]
            );
            if ($res) {
                return [
                    'code'    => 200,
                    'message' => lecho("Success")
                ];
            } else {
                throw new RuleException('失败');
            }
        } catch (Exception $exception) {
            throw new RuleException($exception->getMessage());
        }
    }
}
