<?php

namespace Modules\Payment\Contracts;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Modules\Payment\Models\Payment;
use Modules\Payment\Models\Refund;

interface Paymentable
{
    /**
     * Notes   : 支付订单关联
     *
     * @Date   : 2023/3/22 15:44
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function payments(): MorphMany;

    /**
     * Notes   : 获取支付参数，根据网关和通道
     *
     * @Date   : 2023/5/22 13:28
     * <AUTHOR> <Jason.C>
     * @param  string  $gateway
     * @param  string  $channel
     * @return array
     */
    public function getPaymentParams(string $gateway, string $channel): array;

    /**
     * Notes   : 获取模型的支付标题
     *
     * @Date   : 2023/3/24 17:30
     * <AUTHOR> <Jason.C>
     * @return string
     */
    public function getTitle(): string;

    /**
     * Notes   : 获取支付额
     *
     * @Date   : 2023/3/23 14:58
     * <AUTHOR> <Jason.C>
     * @return string
     */
    public function getTotalAmount(): string;

    /**
     * Notes   : 支付完成的回调
     *
     * @Date   : 2023/3/22 15:42
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Models\Payment  $payment
     * @return bool
     */
    public function paid(Payment $payment): bool;

    /**
     * Notes   : 该订单是否可以支付
     *
     * @Date   : 2023/3/22 15:42
     * <AUTHOR> <Jason.C>
     * @return bool
     */
    public function canPay(): bool;

    /**
     * Notes   : 是否可退款
     *
     * @Date   : 2023/10/16 14:52
     * <AUTHOR> <Jason.C>
     * @return bool
     */
    public function canRefund(): bool;

    /**
     * Notes   : 生成退款单
     *
     * @Date   : 2023/10/17 9:32
     * <AUTHOR> <Jason.C>
     * @param  Refundable  $refundable  退款模型
     * @param  float  $amount  退款金额
     * @param  string|null  $remark  备注信息
     * @return Refund
     */
    public function makeRefund(Refundable $refundable, float $amount, ?string $remark = null): Refund;
}