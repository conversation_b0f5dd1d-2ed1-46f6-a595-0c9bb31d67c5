<?php

namespace Modules\Payment\Contracts;

use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;

interface PaymentAdapter
{
    public function __construct(Payment|null $payment);

    /**
     * Notes   : 获取配置文件
     *
     * @Date   : 2023/4/27 17:14
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function getConfig(): array;

    /**
     * Notes   : 获取回调数据
     *
     * @Date   : 2023/4/27 17:14
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function callback(): array;

    /**
     * Notes   : 回调成功数据返回
     *
     * @Date   : 2023/4/27 17:14
     * <AUTHOR> <Jason.C>
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function success(): ResponseInterface;

    /**
     * Notes   : 查询交易
     *
     * @Date   : 2023/4/27 17:13
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function query(): array;

    /**
     * Notes   : 退款
     *
     * @Date   : 2023/6/6 09:23
     * <AUTHOR> <Jason.C>
     * @param  string  $refundNo  退款单号
     * @param  float  $refundAmount  退款金额
     * @param  string  $desc  退款说明
     * @return array
     */
    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array;
}