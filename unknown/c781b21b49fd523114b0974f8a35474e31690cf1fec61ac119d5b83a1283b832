<?php

namespace Modules\Payment\Console;

use App\Models\User;
use Illuminate\Console\Command;

class InitUserAccount extends Command
{
    protected $signature = 'payment:init-account';

    protected $description = 'Sync exists users to user_infos.';

    public function handle(): void
    {
        foreach (User::all() as $user) {
            if (! $user->account) {
                $user->account()->create();
            }
            if (! $user->security) {
                $user->security()->create([
                    'password' => '000000',
                ]);
            }
        }
    }
}
