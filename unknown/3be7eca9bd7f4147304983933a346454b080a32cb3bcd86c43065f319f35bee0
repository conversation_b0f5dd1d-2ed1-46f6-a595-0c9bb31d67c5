<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Payment\Drivers\WithdrawFactory;

class WithdrawChannel extends Model
{
    use Cachable,
        HasCovers,
        HasEasyStatus,
        SoftDeletes;

    protected $table = 'payment_withdraw_channels';

    protected $casts = [
        'params' => 'json',
    ];

    public function getFactory(): WithdrawFactory
    {
        return new WithdrawFactory($this);
    }
}
