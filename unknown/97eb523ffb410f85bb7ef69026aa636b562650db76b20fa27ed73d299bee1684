<?php

use Illuminate\Support\Facades\Route;
use Modules\Payment\Http\Controllers\Admin\AccountController;
use Modules\Payment\Http\Controllers\Admin\AccountLogController;
use Modules\Payment\Http\Controllers\Admin\AccountRuleController;
use Modules\Payment\Http\Controllers\Admin\BalanceController;
use Modules\Payment\Http\Controllers\Admin\BankCardController;
use Modules\Payment\Http\Controllers\Admin\BankController;
use Modules\Payment\Http\Controllers\Admin\BillController;
use Modules\Payment\Http\Controllers\Admin\ChannelController;
use Modules\Payment\Http\Controllers\Admin\CorporateController;
use Modules\Payment\Http\Controllers\Admin\DashboardController;
use Modules\Payment\Http\Controllers\Admin\PaymentController;
use Modules\Payment\Http\Controllers\Admin\RechargeController;
use Modules\Payment\Http\Controllers\Admin\RefundController;
use Modules\Payment\Http\Controllers\Admin\SecurityController;
use Modules\Payment\Http\Controllers\Admin\VirtualController;
use Modules\Payment\Http\Controllers\Admin\WithdrawChannelController;
use Modules\Payment\Http\Controllers\Admin\WithdrawController;

Route::get('/', [DashboardController::class, 'index']);

Route::get('payments', [PaymentController::class, 'index'])->name('payments.index');
Route::get('payments/{payment}/virtuals', [PaymentController::class, 'virtuals'])->name('payments.virtuals');
Route::get('refunds', [RefundController::class, 'index'])->name('refunds');
Route::get('balances', [BalanceController::class, 'index']);
Route::get('virtuals', [VirtualController::class, 'index']);

Route::get('banks/cards', [BankCardController::class, 'index']);
Route::get('banks/ajax', [BankController::class, 'ajax'])->name('banks.ajax');
Route::resource('banks', BankController::class);

Route::get('accounts', [AccountController::class, 'index']);
Route::get('accounts/logs', [AccountLogController::class, 'index']);
Route::get('accounts/recharges', [RechargeController::class, 'index']);
Route::get('accounts/rules/ajax', [AccountRuleController::class, 'ajax'])->name('rules.ajax');
Route::resource('accounts/rules', AccountRuleController::class);
Route::get('securities', [SecurityController::class, 'index']);

Route::get('bills/day', [BillController::class, 'day']);
Route::get('bills/week', [BillController::class, 'week']);
Route::get('bills/month', [BillController::class, 'month']);
Route::get('bills/quarter', [BillController::class, 'quarter']);
Route::get('bills/year', [BillController::class, 'year']);

Route::get('withdraws', [WithdrawController::class, 'index']);
Route::resource('withdraws/channels', WithdrawChannelController::class);

Route::get('channels', [ChannelController::class, 'index'])->name('channels');
Route::get('corporates', [CorporateController::class, 'index']);
