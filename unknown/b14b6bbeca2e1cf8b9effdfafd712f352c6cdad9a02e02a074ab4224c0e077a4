<?php

namespace Modules\Payment\Models\Traits;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Payment\Models\Payment;

trait BelongsToPayment
{
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    public function setPaymentAttribute(Payment $payment): void
    {
        $this->attributes['payment_id'] = $payment->getKey();
    }
}