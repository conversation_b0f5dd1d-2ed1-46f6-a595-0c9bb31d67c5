<?php

namespace Modules\Socialite\Http\Controllers\Api\Wechat;

use App\Facades\Api;
use App\Models\User;
use App\Packages\XinHuaERP\XinHuaERP;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Socialite\Http\Controllers\Api\Controller;
use Modules\Socialite\Http\Requests\OpenBindMobileRequest;
use Modules\Socialite\Models\Wechat;
use Overtrue\Socialite\Exceptions\AuthorizeFailedException;

class OpenController extends Controller
{
    public function index(): JsonResponse
    {
        return $this->success();
    }

    public function getAppId()
    {
        return $this->success([
            'app_id' => config('socialite.WECHAT_OPEN_APP_ID'),
        ]);
    }

    /**
     * 获取授权地址
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function oauth(Request $request)
    {
        $redirect_uri = $request->callback ?: '';
        $oauth        = app('wechat.open_platform')->getOAuth();
        $redirectUrl  = $oauth->scopes(['snsapi_login'])
            ->redirect($redirect_uri);
        return $this->success([
            'redirect_url' => $redirectUrl,
        ]);
    }

    /**
     * 开放平台CODE登录
     *
     * @param  \Modules\Socialite\Http\Requests\CodeRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function code(Request $request): JsonResponse
    {
        try {
            if (! $request->code) {
                return $this->failed('请传入CODE');
            }
            $oauth = app('wechat.open_platform')->getOAuth();

            $user = $oauth->userFromCode($request->code);
            Wechat::updateOrCreate([
                'wx_openid' => $user->getTokenResponse()['openid'],
            ], [
                'user_id'  => Api::id() ?? 0,
                'union_id' => $user['raw']['union_id'] ?? null,
                'nickname' => $user->getNickname(),
                'avatar'   => $user->getAvatar(),
                'gender'   => $user['raw']['sex'] ?? 0,
            ]);
            XinHuaERP::user()->addVisitor($user['raw']['union_id'] ?? '');

            return $this->success([
                'openid'   => $user->getTokenResponse()['openid'],
                'nickname' => $user->getNickname(),
                'avatar'   => $user->getAvatar(),
            ]);
        } catch (AuthorizeFailedException) {
            return $this->failed('授权失败，CODE可能已经被使用');
        } catch (Exception $exception) {
            return $this->failed($exception->getMessage());
        }
    }

    public function bindWechat(Request $request)
    {
        try {
            $user = Api::user();
            if ($user->getUnionId()) {
                return $this->failed('该用户已经绑定微信！');
            }
            if (! $request->code) {
                return $this->failed('请传入CODE');
            }
            $oauth     = app('wechat.open_platform')->getOAuth();
            $oauthUser = $oauth->userFromCode($request->code);
            $openId    = $oauthUser->getId();
            $raw       = $oauthUser->getRaw();
            $unionId   = $raw['unionid'] ?? null;
            $user->wechat()->updateOrCreate([
                'user_id' => $user->id,
            ], [
                'union_id'        => $unionId,
                'platform_openid' => $openId,
                'nickname'        => $oauthUser->getNickname(),
                'avatar'          => $oauthUser->getAvatar(),
                'gender'          => $raw['sex'] ?? 0,
            ]);
            XinHuaERP::user()->addVisitor($unionId ?: '');

            return $this->success([
                'message' => lecho('Binding successful')
            ]);
        } catch (AuthorizeFailedException) {
            return $this->failed(lecho('Authorization failed, CODE may have already been used'));
        } catch (Exception $exception) {
            return $this->failed($exception->getMessage());
        }
    }

    public function login(Request $request): JsonResponse
    {
        try {
            if (! $request->code) {
                return $this->failed('请传入CODE');
            }
            $oauth = app('wechat.open_platform')->getOAuth();

            $oauthUser = $oauth->userFromCode($request->code);
            $openId    = $oauthUser->getId();
            $raw       = $oauthUser->getRaw();
            $unionId   = $raw['unionid'] ?? null;

            if ($unionId) {
                $wehcat = Wechat::firstOrCreate([
                    'union_id' => $unionId,
                ], [
                    'platform_openid' => $openId,
                    'user_id'         => 0,
                    'nickname'        => $oauthUser->getNickname(),
                    'avatar'          => $oauthUser->getAvatar(),
                    'gender'          => $raw['sex'] ?? 0,
                ]);
            } else {
                $wehcat = Wechat::firstOrCreate([
                    'platform_openid' => $openId,
                ], [
                    'union_id' => $unionId,
                    'user_id'  => 0,
                    'nickname' => $oauthUser->getNickname(),
                    'avatar'   => $oauthUser->getAvatar(),
                    'gender'   => $raw['sex'] ?? 0,
                ]);
            }
            $data = [
                'need_mobile' => true,
                'token'       => '',
                'is_new'      => true,
                'type'        => 'Bearer',
                'union_id'    => $unionId,
            ];
            if ($wehcat->user_id > 0) {
                $user                = $wehcat->user;
                $token               = $this->loginUser($user);
                $data['need_mobile'] = false;
                $data['token']       = $token->plainTextToken;
                $data['is_new']      = false;
            }

            return $this->success($data);
        } catch (AuthorizeFailedException $exception) {
            info($exception->getMessage());
            return $this->failed('授权失败，CODE可能已经被使用');
        } catch (Exception $exception) {
            return $this->failed($exception->getMessage());
        }
    }

    public function bindMobile(OpenBindMobileRequest $request)
    {
        $username = $request->username;
        $unionId  = $request->union_id;
        $user     = User::where('username', $username)->first();
        $wechat   = Wechat::where('union_id', $unionId)->first();
        $isNew    = true;
        if ($user) {
            $isNew = false;

            if ($user->wechat) {
                if ($user->wechat->union_id) {
                    return $this->failed('该用户已经绑定微信账号');
                } elseif ($user->wechat->id != $wechat->id) {
                    $user->wechat->delete();
                    $wechat->user_id = $user->id;
                    $wechat->save();
                }
            } else {
                $wechat->user_id = $user->id;
                $wechat->save();
            }
        } else {
            $user = User::create([
                'username'     => $username,
                'wechat_model' => $wechat,
            ]);
        }
        $token = $this->loginUser($user);
        return $this->success([
            'token'  => $token->plainTextToken,
            'is_new' => $isNew,
            'type'   => 'Bearer',
        ]);
    }
}
