<?php

namespace Modules\Storage\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    protected string $moduleNamespace = 'Modules\Storage\Http\Controllers';

    public function map(): void
    {
        $this->mapAdminRoutes();
        $this->mapApiRoutes();
    }

    protected function mapAdminRoutes(): void
    {
        Route::as(config('admin.route.as').'storage.')
            ->domain(config('admin.route.domain'))
            ->middleware(config('admin.route.middleware'))
            ->namespace($this->moduleNamespace.'\Admin')
            ->prefix(config('admin.route.prefix').'/storage')
            ->group(__DIR__.'/../Routes/admin.php');
    }

    protected function mapApiRoutes(): void
    {
        Route::as(config('api.route.as').'storage.')
            ->domain(config('api.route.domain'))
            ->middleware(config('api.route.middleware'))
            ->namespace($this->moduleNamespace)
            ->prefix(config('api.route.prefix').'/storage')
            ->group(__DIR__.'/../Routes/api.php');
    }
}
