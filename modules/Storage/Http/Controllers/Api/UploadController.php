<?php

namespace Modules\Storage\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use Modules\Storage\Http\Requests\UploadRequest;
use Modules\Storage\Http\Requests\UploadsRequest;
use Modules\Storage\Models\Upload;

class UploadController extends ApiController
{
    protected string $path;

    protected array $imageMimes = [
        'image/png',
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/bmp',
        'image/webp',
    ];

    public function __construct()
    {
        $this->path = date('Y/m/d');
    }

    /**
     * Notes   : 上传单个文件
     *
     * @Date   : 2023/9/12 15:00
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Storage\Http\Requests\UploadRequest  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function upload(UploadRequest $request): JsonResponse
    {
        $file = $request->safe()->file;
        $info = $this->save($file);
        return $this->success($info);
    }

    /**
     * Notes   : 单文件/多文件上传
     *
     * @Date   : 2023/1/3 17:32
     * <AUTHOR> <Jason.C>
     */
    public function uploads(UploadsRequest $request): JsonResponse
    {
        $files = $request->safe()->files;

        $asSave = [];
        foreach ($files as $file) {
            $asSave[] = $this->save($file);
        }

        return $this->success($asSave);
    }

    /**
     * @throws \Exception
     */
    protected function save(UploadedFile $file)
    {
        $hash = File::hash($file);
        $extension = $file->guessClientExtension() ?? 'jpg';
        $name = sprintf('%s.%s', $hash, $extension);
        $path = sprintf('%s/%s', $this->path, $name);

        $existFile = Upload::where('hash', $hash)->first();

        if (! $existFile) {
            if (Storage::putFileAs($this->path, $file, $name) === false) {
                throw new Exception('文件上传失败', 4040);
            }

            Upload::create([
                'user_id'  => Api::id(),
                'hash'     => $hash,
                'size'     => File::size($file),
                'type'     => $file->getClientMimeType(),
                'original' => $file->getClientOriginalName(),
                'disk'     => config('filesystems.default'),
                'path'     => $path,
            ]);

            if (config('storage.AUTO_MAKE_THUMB') && in_array($file->getMimeType(), $this->imageMimes)) {
                $thumb = Image::make($file)
                    ->resize(config('admin.thumb_size'), config('admin.thumb_size'), function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    })
                    ->encode($file->guessClientExtension());

                $thumbName = sprintf('%s/%s-thumb.%s', $this->path, $hash, $extension);
                Storage::put($thumbName, $thumb);
                $thumb->destroy();
            }

            return [
                'hash'     => $hash,
                'type'     => $file->getMimeType(),
                'size'     => File::size($file),
                'original' => $file->getClientOriginalName(),
                'url'      => Storage::url($path),
                'path'     => $path,
            ];
        } else {
            return [
                'hash'     => $hash,
                'type'     => $existFile->type,
                'size'     => $existFile->size,
                'original' => $existFile->original,
                'url'      => Storage::url($existFile->path),
                'path'     => $existFile->path,
            ];
        }
    }
}