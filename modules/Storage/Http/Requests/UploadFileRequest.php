<?php

namespace Modules\Storage\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class UploadFileRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'file' => [
                'bail',
                'required',
                'file',
                'mimes:txt,markdown,pdf,html,xlsx,xls,docx,doc,pptx,ppt,csv,aac,mp3,mp4,wav,wma,wmv,avi,mpg,mpeg,jpg,jpeg,png,gif,bmp,webp,md,mov',
                'max:'.config('storage.FRONT_MAX_FILE_SIZE'),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'file.required' => '文件必须上传',
            'file.file'     => '文件格式不正确',
            'file.mimes'    => '文件格式不允许',
            'file.max'      => '文件大小不可超过 :max KB',
        ];
    }
}