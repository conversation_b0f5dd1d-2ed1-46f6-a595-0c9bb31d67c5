<?php

namespace Modules\Storage\Adapters;

use DateTimeInterface;
use Exception;
use Illuminate\Support\Carbon;
use League\Flysystem\Config;
use League\Flysystem\FileAttributes;
use League\Flysystem\UnableToCopyFile;
use League\Flysystem\UnableToMoveFile;
use League\Flysystem\UnableToRetrieveMetadata;
use League\Flysystem\UnableToWriteFile;
use League\Flysystem\Visibility;
use Modules\Storage\Exceptions\ConfigException;
use Qcloud\Cos\Client;

/**
 * 腾讯云存储
 */
class CosAdapter extends CoreAdapter
{
    public function initClient(): void
    {
        $config = [
            'credentials' => [
                'appId'     => $this->config['COS_APP_ID'],
                'secretId'  => $this->config['COS_SECRET_ID'],
                'secretKey' => $this->config['COS_SECRET_KEY'],
            ],
            'region'      => $this->config['COS_REGION'],
            'schema'      => $this->config['COS_USE_SSL'] ? 'https' : 'http',
        ];

        $this->cosClient = new Client($config);

        $this->appId     = $this->config['COS_APP_ID'];
        $this->bucket    = $this->config['COS_BUCKET'].'-'.$this->config['COS_APP_ID'];
        $this->region    = $this->config['COS_REGION'];
        $this->useSSL    = ! ! $this->config['COS_USE_SSL'];
        $this->isCname   = ! ! $this->config['COS_IS_CNAME'];
        $this->cdnHost   = $this->config['COS_CDN_HOST'];
        $this->signedUrl = ! ! $this->config['COS_SIGNED_URL'];
    }

    /**
     * @throws \Exception
     */
    public function getUrl(string $path): string
    {
        if ($this->signedUrl) {
            return $this->getTemporaryUrl($path, Carbon::now()->addHour());
        }

        if ($this->isCname && is_null($this->cdnHost)) {
            throw new ConfigException('CDN_HOST未配置');
        }

        $path = $this->pathPrefixer->prefixPath($path);

        return $this->cosClient->getObjectUrlWithoutSign($this->bucket, $path);
    }

    public function getTemporaryUrl(
        string $path,
        DateTimeInterface $expiration = null,
        array $options = []
    ): bool|string {
        $path = $this->pathPrefixer->prefixPath($path);

        return $this->cosClient->getObjectUrl($this->bucket, $path, $expiration);
    }

    protected function getMetadata(string $path): FileAttributes
    {
        $path = $this->pathPrefixer->prefixPath($path);

        $result = $this->cosClient->headObject(array(
            'Bucket' => $this->bucket,
            'Key'    => $path,
        ));

        $size      = isset($result['ContentLength']) ? intval($result['ContentLength']) : 0;
        $timestamp = isset($result['LastModified']) ? strtotime($result['LastModified']) : 0;
        $mimetype  = $result['ContentType'] ?? '';

        $acl        = $this->cosClient->getObjectAcl([
            'Bucket' => $this->bucket,
            'Key'    => $path,
        ]);
        $visibility = $acl['Grants'][0]['Grant']['Permission'] == 'FULL_CONTROL' ? Visibility::PUBLIC : Visibility::PRIVATE;

        return new FileAttributes($path, $size, $visibility, $timestamp, $mimetype);
    }

    public function fileExists(string $path): bool
    {
        $path = $this->pathPrefixer->prefixPath($path);

        return $this->cosClient->doesObjectExist($this->bucket, $path);
    }

    public function directoryExists(string $path): bool
    {
        return $this->fileExists($path);
    }

    public function write(string $path, string $contents, Config $config): void
    {
        $path = $this->pathPrefixer->prefixPath($path);
        // $options = $config->get('options', []);

        try {
            $this->cosClient->putObject([
                'Bucket' => $this->bucket,
                'Key'    => $path,
                'Body'   => $contents,
            ]);
        } catch (Exception $exception) {
            throw UnableToWriteFile::atLocation($path, $exception->getMessage(), $exception);
        }
    }

    public function writeStream(string $path, $contents, Config $config): void
    {
        $this->write($path, stream_get_contents($contents), $config);
    }

    public function read(string $path): string
    {
        $path = $this->pathPrefixer->prefixPath($path);

        return file_get_contents($this->cosClient->getObjectUrl($this->bucket, $path));
    }

    public function readStream(string $path)
    {
//        $stream = fopen('php://temp', 'w+b');
//
//        try {
//            fwrite($stream, $this->cosClient->getObject([
//                'Bucket' => $this->bucket,
//                'Key'    => $path,
//            ]));
//        } catch (Exception $exception) {
//            fclose($stream);
//            throw UnableToReadFile::fromLocation($path, $exception->getMessage(), $exception);
//        }
//        rewind($stream);
//        return $stream;
    }

    public function delete(string $path): void
    {
        $path = $this->pathPrefixer->prefixPath($path);

        $this->cosClient->deleteObject([
            'Bucket' => $this->bucket,
            'Key'    => $path,
        ]);
    }

    public function deleteDirectory(string $path): void
    {
        $this->delete($path);
    }

    public function createDirectory(string $path, Config $config): void
    {
        $path = $this->pathPrefixer->prefixPath($path);
        $this->cosClient->putObject([
            'Bucket' => $this->bucket,
            'Key'    => $path.'/',
            'Body'   => '',
        ]);
    }

    public function setVisibility(string $path, string $visibility): void
    {
        $path = $this->pathPrefixer->prefixPath($path);
        $this->cosClient->putObjectAcl([
            'Bucket' => $this->bucket,
            'Key'    => $path,
            'ACL'    => $visibility == Visibility::PRIVATE ? 'private' : 'public-read'
        ]);
    }

    public function visibility(string $path): FileAttributes
    {
        $meta = $this->getMetadata($path);
        if (null === $meta->visibility()) {
            throw UnableToRetrieveMetadata::visibility($path);
        }

        return $meta;
    }

    public function mimeType(string $path): FileAttributes
    {
        $meta = $this->getMetadata($path);
        if (null === $meta->mimeType()) {
            throw UnableToRetrieveMetadata::mimeType($path);
        }

        return $meta;
    }

    public function lastModified(string $path): FileAttributes
    {
        $meta = $this->getMetadata($path);
        if (null === $meta->lastModified()) {
            throw UnableToRetrieveMetadata::lastModified($path);
        }

        return $meta;
    }

    public function fileSize(string $path): FileAttributes
    {
        $meta = $this->getMetadata($path);
        if (null === $meta->fileSize()) {
            throw UnableToRetrieveMetadata::fileSize($path);
        }

        return $meta;
    }

    public function listContents(string $path, bool $deep): iterable
    {
        $directory = $this->pathPrefixer->prefixDirectoryPath($path);

        $response = $this->cosClient->listObjects([
            'Bucket'       => $this->bucket,
            'Delimiter'    => '/',
            'EncodingType' => 'url',
            'Marker'       => '',
            'Prefix'       => $directory,
            'MaxKeys'      => 1000,
        ]);
        foreach ($response['Contents'] ?? [] as $content) {
            yield new FileAttributes(
                $content['Key'],
                intval($content['Size']),
                null,
                strtotime($content['LastModified'])
            );
        }
//        foreach ($response['CommonPrefixes'] ?? [] as $prefix) {
//            yield new DirectoryAttributes($prefix['Prefix']);
//        }
    }

    public function move(string $source, string $destination, Config $config): void
    {
        try {
            $this->copy($source, $destination, $config);
            $this->delete($source);
        } catch (Exception $exception) {
            throw UnableToMoveFile::fromLocationTo($source, $destination, $exception);
        }
    }

    public function copy(string $source, string $destination, Config $config): void
    {
        try {
            $this->cosClient->copy(
                $this->bucket,
                $destination,
                [
                    'Region' => $this->region,
                    'Bucket' => $this->bucket,
                    'Key'    => $source,
                ]
            );
        } catch (Exception $exception) {
            throw UnableToCopyFile::fromLocationTo($source, $destination, $exception);
        }
    }
}