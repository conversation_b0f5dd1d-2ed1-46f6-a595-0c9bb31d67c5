<?php

namespace Modules\Company\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Company\Events\CompanyCreated;

class CompanyCreatedListener implements ShouldQueue
{

    public string $queue = 'COMPANY';

    /**
     * Notes   : 企业申请之后触发
     *
     * @Date   : 2021/7/19 16:07
     * <AUTHOR> Mr.wang
     * @param  CompanyCreated  $event
     */
    public function handle(CompanyCreated $event): void
    {
    }

}
