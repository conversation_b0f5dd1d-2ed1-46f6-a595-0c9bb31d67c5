<?php

namespace Modules\Company\Traits;

use App\Exceptions\ValidatorException;
use App\Models\Realname;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Modules\Company\Http\Requests\BatchAddUserRequest;
use Modules\Company\Models\Business;
use Modules\Company\Models\Company;
use Modules\Company\Models\Staff;

trait CompanyTraits
{
    public function checkCompany($company)
    {
        if (is_numeric($company)) {
            $company = Company::find($company);
        }
        if (! $company) {
            throw new ValidatorException("企业不存在");
        }

        if ($company->status == Company::STATUS_UNCHECK) {
            throw new ValidatorException("企业待审核");
        }

        if ($company->status == Company::STATUS_REJECT) {
            throw new ValidatorException("该企业还未通过审核");
        }

        if ($company->status == Company::STATUS_CLOSE) {
            throw new ValidatorException("企业关闭");
        }

        if (! $company->isCertification()) {
            throw new ValidatorException("该企业还未认证");
        }
    }

    public function getTypeName($type)
    {
        return (new Staff())->getTypeName($type);
    }

    /**
     * Notes: 读取文件
     *
     * @Author: 玄尘
     * @Date: 2024/12/31 16:00
     * @param $link
     * @param $company
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function readUrl($link, $company)
    {
        $fileContents = file_get_contents($link, false, stream_context_create([
            'http' => [
                'method'  => 'GET',
                'timeout' => 30, // 设置超时时间为30秒
            ],
        ]));

        if ($fileContents === false) {
            throw new ValidatorException("无法读取远程文件");
        }

        $tmpFile = tempnam(sys_get_temp_dir(), 'excel');
        file_put_contents($tmpFile, $fileContents);

        $rowData = excel($tmpFile);

        $imUsers = [];
        foreach ($rowData as $k => &$v) {
            $phone = $v[1];
            $phone = $phone ? Str::replace('.0', '', $phone) : '';

            $info = [
                'nickname'     => $v[0] ?? '',
                'phone'        => $phone,
                'company_name' => $company->name,
                'position'     => $v[2] ?? '',
                'industry'     => $v[3] ?? '',
                'wechat'       => $v[4] ?? '',
                'email'        => $v[5] ?? '',
                'address'      => $v[6] ?? '',
                'status'       => 1,
                'message'      => '正常',
            ];

            $batchAddUserRequest = new BatchAddUserRequest();

            $validator = Validator::make($info,
                $batchAddUserRequest->rules(),
                $batchAddUserRequest->messages()
            );

            if ($validator->fails()) {
                $errors          = $validator->errors()->all();
                $info['message'] = implode(",", $errors);
                $info['status']  = 0;
            }
            unlink($tmpFile);
            $imUsers[] = $info;
        }

        return $imUsers;
    }

    /**
     * Notes: 检查是否实名认证
     *
     * @Author: 玄尘
     * @Date: 2024/12/31 16:00
     * @param $user
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    private function checkRealname($user)
    {
        $realname = Realname::where('uid', $user->id)->first();
        if (! $realname) {
            throw new ValidatorException("请先进行实名认证");
        }
        if ($realname->is_check == 0) {
            throw new ValidatorException("实名认证正在审核中");
        }
        if ($realname->is_check == 2) {
            throw new ValidatorException("实名认证审核没通过，请先重新认证实名认证");
        }
        return $realname;
    }

    /**
     * Notes: 用户认证
     *
     * @Author: 玄尘
     * @Date: 2024/12/31 16:00
     * @param $user
     * @param $legalIdCard
     * @param $phone
     * @param $legalName
     * @throws \App\Exceptions\ValidatorException
     */
    private function checkUserCertification($user, $legalIdCard, $phone, $legalName): void
    {
        $checkUser = alibaba(
            url: 'https://mobilecert.market.alicloudapi.com/mobile3MetaSimple',
            paramsType: "query",
            params: [
                'identifyNum' => $legalIdCard,
                'mobile'      => $phone,
                'userName'    => $legalName,
            ]
        );

        switch ($checkUser['bizCode']) {
            case 2:
                throw new ValidatorException("个人三要素校验不一致");
            case 3:
                throw new ValidatorException("查无记录");
            default:
                break;
        }
    }

    public function checkBusinessByCreateCompany($business_id, $user)
    {
        if ($business_id) {
            $business = Business::find($business_id);
        } else {
            $business = Business::where('user_id', $user->id)->first();
        }
        if (! $business) {
            throw new ValidatorException("未查询到名片信息");
        }

        if ($business->company_id) {
            throw new ValidatorException("该名片已认证过企业");
        }

        if ($business->isWork()) {
            throw new ValidatorException("该名片已经职位认证过");
        }
        if ($business->user_id != $user->id) {
            throw new ValidatorException("该名片不属于当前用户");
        }
        return $business;
    }
}
