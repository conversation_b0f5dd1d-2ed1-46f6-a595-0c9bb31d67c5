<?php

namespace Modules\Company\Http\Resources\Api\BusinessBrowseRecord;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\Models\BusinessContact;

class MyRecordResource extends JsonResource
{

    public function toArray($request): array
    {
        return [
            'id'                => $this->business_id,
            'nickname'          => $this->business->nickname,
            'is_work'           => $this->business->is_work,
            'is_company'        => $this->business->is_company,
            'avatar'            => $this->business->avatar,
            'count'             => $this->business->count,
            'company_name'      => $this->business->company_name,
            'annex'             => $this->business->annex ?? '',
            'annex_name'        => $this->business->annex_name ?? '',
            'position'          => $this->business->position,
            'visitor_time'      => (string) $this->created_at,
            'change_status'     => BusinessContact::getChangeStatus(
                $this->business->user_id,
                $this->business_id,
                $this->user_id,
            ),
            'is_realname_check' => $this->realname ? 1 : 0,
        ];
    }

}
