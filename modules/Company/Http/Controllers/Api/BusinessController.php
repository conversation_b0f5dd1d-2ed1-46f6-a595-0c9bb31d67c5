<?php

namespace Modules\Company\Http\Controllers\Api;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Models\Realname;
use App\Models\User;
use App\Models\UserPrivacy;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Modules\Company\Http\Requests\Business\AddBusinessRequest;
use Modules\Company\Http\Resources\Api\Business\BusinessResource;
use Modules\Company\Http\Resources\Api\BusinessBrowseRecord\MyRecordCollection;
use Modules\Company\Http\Resources\Api\BusinessBrowseRecord\RecordMyCollection;
use Modules\Company\Models\Business;
use Modules\Company\Models\BusinessBrowseRecord;
use Modules\Company\Models\BusinessContact;
use Modules\Company\Models\BusinessStyle;
use Modules\Company\Models\Company;
use Modules\Company\Models\Staff;
use Modules\Company\Traits\BusinessTrait;
use Modules\Interaction\Models\Like;
use Modules\User\Models\UserInfo;

class BusinessController extends ApiController
{
    use BusinessTrait;

    public function my(Request $request)
    {
        $type = $request->type ?? 0;

        $user = $request->kernel->user();
        //已经关联公司的名片
        $businesses = $user->jzBusinesses()
            ->with('staff', 'user.realName', 'style')
            ->when($type == 1, function ($query) {
                return $query->where('company_id', '>', 0);
            })
            ->latest()
            ->get();

        return $request->kernel->success(BusinessResource::collection($businesses));
    }

    /**
     * Notes: 添加名片
     *
     * @Author: 玄尘
     * @Date: 2024/12/24 15:15
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function create(Request $request)
    {
        $validate = new AddBusinessRequest();
        $request->kernel->validate($validate->rules(), $validate->messages());

        $user = $request->kernel->user();

        $this->canDepartmentAddBusiness($user);

        $business      = $request->business;//业务介绍 business_type不为0时候 传数组
        $business_type = $request->business_type;
        $company       = $request->company;
        $company_name  = $request->company_name;
        $company_type  = $request->company_type;
        $avatar        = $request->avatar;
        $business_id   = $request->business_id ?? '';

        if ($company_type != 0) {
            $company = json_encode($company);
        }
        if ($business_type != 0) {
            $business = json_encode($business);
        }

        $businessData = [
            'avatar'        => $avatar ?? $user->info->avatar_url,
            'industry_id'   => $request->industry_id ?? 0,
            'nickname'      => $request->nickname,
            'company_name'  => $company_name,
            'company_icon'  => $request->company_icon ?? '',
            'phone'         => $request->phone,
            'wechat'        => $request->wechat,
            'email'         => $request->email,
            'address'       => $request->address,
            'introduction'  => $request->introduction,
            'annex'         => $request->annex,
            'annex_name'    => $request->annex_name,
            'business'      => $business,
            'business_type' => $request->business_type,
            'company'       => $company,
            'company_type'  => $request->company_type,
            'position'      => $request->position,
            'wiki'          => $request->wiki,
        ];

        $businessStyleData = [
            'nickname_show'      => $request->nickname_show ?? 1,
            'email_show'         => $request->email_show ?? 0,
            'email_color'        => $request->email_color ?? null,
            'email_bold'         => $request->email_bold ?? 0,
            'email_size'         => $request->email_size ?? null,
            'company_icon_show'  => $request->company_icon_show ?? 1,
            'company_name_show'  => $request->company_name_show ?? 1,
            'company_name_size'  => $request->company_name_size ?? null,
            'company_name_bold'  => $request->company_name_bold ?? 0,
            'company_name_color' => $request->company_name_color ?? '',
            'avatar_show'        => $request->avatar_show ?? 1,
            'address_show'       => $request->address_show ?? 0,
            'address_bold'       => $request->address_bold ?? 0,
            'address_color'      => $request->address_color ?? null,
            'address_size'       => $request->address_size ?? null,
            'phone_show'         => $request->phone_show ?? 1,
            'phone_size'         => $request->phone_size ?? null,
            'phone_bold'         => $request->phone_bold ?? 0,
            'phone_color'        => $request->phone_color ?? '',
            'nickname_size'      => $request->nickname_size ?? null,
            'nickname_bold'      => $request->nickname_bold ?? 0,
            'nickname_color'     => $request->nickname_color ?? '',
            'position_show'      => $request->position_show ?? 1,
            'position_bold'      => $request->position_bold ?? 0,
            'position_color'     => $request->position_color ?? '',
            'position_size'      => $request->position_size ?? 12,
            'wiki_bold'          => $request->wiki_bold ?? 0,
            'wiki_color'         => $request->wiki_color ?? null,
            'wiki_size'          => $request->wiki_size ?? null,
            'background'         => $request->background ?? '',
            'layout'             => $request->layout ?? '',
        ];

        DB::beginTransaction();
        try {
            if ($business_id) {
                $businessModel = Business::find($business_id);
                if (! $businessModel) {
                    throw new ValidatorException("要修改的名片不存在");
                }

                if ($businessModel->user->isNot($user)) {
                    throw new ValidatorException("您没有权限修改此名片");
                }

                $businessModel->update($businessData);
                $businessModel->style->update($businessStyleData);
            } else {
                $companyModel = Company::where('name', $company_name)->first();
                if ($companyModel) {
                    $businessData['company_id'] = $companyModel->id;
                }
                $defaultBusiness = $user->jzBusinesses()
                    ->where('is_default', 1)
                    ->first();

                if (! $defaultBusiness) {
                    $businessData['is_default'] = 1;
                }

                $businessModel = $user->jzBusinesses()->create($businessData);
                $businessModel->style()->create($businessStyleData);

                if ($companyModel) {
                    $staff = Staff::where('user_id', $user->id)
                        ->where('business_id', 0)
                        ->where('company_id', $companyModel->id)
                        ->first();
                    if ($staff) {
                        $staff->update([
                            'business_id' => $businessModel->id,
                        ]);
                    }
                }
            }

            DB::commit();  //提交事务
            return $request->kernel->success(true);
        } catch (ValidatorException $e) {
            DB::rollBack();  // 回滚事务
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 删除名片
     *
     * @Author: 玄尘
     * @Date: 2024/12/24 16:35
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function delete(Request $request)
    {
        $this->checkBusinessById($request);

        $user        = $request->kernel->user();
        $business_id = $request->business_id;

        $business = Business::find($business_id);

        $this->checkBusiness($business, $user, '名片不存在', '只能删除自己的名片');
        $business->delete();
        $business->staff()->update([
            'business_id' => 0
        ]);

        return $request->kernel->success('删除成功');
    }

    /**
     * Notes: 切换名片
     *
     * @Author: 玄尘
     * @Date: 2024/12/27 09:48
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function switch(Request $request)
    {
        $this->checkBusinessById($request);

        $user        = $request->kernel->user();
        $business_id = $request->business_id;

        $business = Business::find($business_id);

        $this->checkBusiness($business, $user, '名片不存在', '只能切换自己的名片');

        try {
            Business::where('user_id', $business->user_id)
                ->update([
                    'is_default' => 0
                ]);
            $business->update([
                'is_default' => 1
            ]);
            return $request->kernel->success(true);
        } catch (ValidatorException $e) {
            throw new ValidatorException($e->getMessage());
        }
    }

    /**
     * Notes: 复制名片
     *
     * @Author: 玄尘
     * @Date: 2024/12/27 09:49
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function copy(Request $request)
    {
        $this->checkBusinessById($request);

        $user        = $request->kernel->user();
        $business_id = $request->business_id;

        $business = Business::find($business_id);

        $this->checkBusiness($business, $user, '名片不存在', '只能复制自己的名片');

        $newBusiness             = $business->replicate();
        $newBusiness->is_default = 0;
        $newBusiness->company_id = null;
        $newBusiness->save();

        $newBusinessStyle              = $business->style->replicate();
        $newBusinessStyle->business_id = $newBusiness->id;
        $newBusinessStyle->save();

        return $request->kernel->success(new BusinessResource($newBusiness));
    }

    public function detail(Request $request)
    {
        $this->checkBusinessById($request);

        $user        = $request->kernel->user();
        $business_id = $request->business_id;

        $business = Business::find($business_id);

//        $this->checkBusiness($business, $user);

        return $request->kernel->success(new BusinessResource($business));
    }

    /**
     * Notes: 发送名片
     *
     * @Author: 玄尘
     * @Date: 2024/12/27 09:52
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function send(Request $request)
    {
        $this->checkBusinessById($request);

        $request->kernel->validate([
            'num' => 'nullable|integer',
        ], [
            'num.integer' => '发送次数只能是数字',
        ]);

        $user        = $request->kernel->user();
        $business_id = $request->business_id;
        $num         = $request->num ?? 1;

        $business = Business::find($business_id);

        $this->checkBusiness($business, $user);

        $business->send_count = $business->send_count + $num;
        $business->save();

        return $request->kernel->success([
            'send_num' => $business->send_count
        ]);
    }

    /**
     * Notes: 浏览名片
     *
     * @Author: 玄尘
     * @Date: 2024/12/24 17:19
     * @param  \Illuminate\Http\Request  $request
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function browse(Request $request)
    {
        $this->checkBusinessById($request);

        $userId = $request->kernel->id();
        $user   = $request->kernel->user();

        $business_id = $request->business_id;
        $pass        = $request->pass ?? '';

        $business = Business::with(['style'])->find($business_id);
        $this->checkBusiness($business);
        //判断需要密码的
        $privacy = UserPrivacy::where('uid', $business->uid)->first();
        if ($privacy && ! $pass) {
            throw new ValidatorException('需要密码才能查看');
        }
        //查看是否有浏览记录
        BusinessBrowseRecord::browse($business, $user);

        $isRealname            = Realname::where('uid', $business->user_id)
            ->where('is_check', 1)
            ->first();
        $business->is_realname = $isRealname ? 1 : 0;

        //获取点赞前5人的头像
        $ids = Like::OfItem($business)->take(5)->pluck('user_id');

        //对隐藏数据进行处理
        $data = BusinessStyle::processData($business->toArray());

        $data['position'] = $business->position;
        $data['user_id']  = $business->user_id;

        if ($business->isWork()) {
            $company = $business->companyModel;

            if ($company && $company->is_check == 1) {
                $data['company_name']  = $company->company_name;
                $data['company_type']  = $company->company_type;
                $data['company']       = $company->getCompany();
                $data['business_type'] = $company->business_type;
                $data['business']      = $company->getBusiness();
            }
        }

        $data['browse_avatar'] = UserInfo::whereIn('user_id', $ids)
            ->get()
            ->map(function ($user) {
                return $user->avatar_url;
            });

        //获取浏览总人数
        $data['browse_count'] = BusinessBrowseRecord::where('business_id', $business->id)->count();

        //获取点赞人数
        $data['like_count'] = Like::OfItem($business)->count();
        $data['is_like']    = 0;

        $like = Like::OfItem($business)->OfUser($userId)->first();

        if ($like) {
            $data['is_like'] = 1;
        }

        //未建立联系
        $is_change_status  = BusinessContact::getChangeStatus($business->user_id, $business->id, $user->id);
        $data['is_change'] = $is_change_status;

        if ($business->user_id == $user->id) {
            $data['is_star'] = 1;
            return $request->kernel->success($data);
        }
        $data['is_star'] = 1;
        if (! $privacy) {
            $data['is_star'] = 1;
            return $request->kernel->success($data);
        }

        if ($pass) {
            $userPass = User::where(['id' => $business->user_id])->value('pass');
            if ($pass == $userPass) {
                $data['is_star'] = 1;
                return $data;
            } else {
                throw new ValidatorException("密码错误");
            }
        }

        if (in_array($is_change_status, [
            BusinessContact::STATUS_NO_LINK,
            BusinessContact::STATUS_WAITING_THEIR_APPROVAL,
            BusinessContact::STATUS_NO_BUSINESS
        ])) {
            $data['is_company_name'] = 0;
            if ($privacy->company_name == 1 && ! empty($data['company_name'])) {
                $data['is_star']         = 0;
                $data['is_company_name'] = 1;
                $data['company_name']    = "******";
            }
            $data['is_phone'] = 0;
            if ($privacy->phone == 1 && ! empty($data['phone'])) {
                $data['is_star']  = 0;
                $data['is_phone'] = 1;
                $data['phone']    = maskData($data['phone'], 0, -4);
            }
            $data['is_nickname'] = 0;
            if ($privacy->nickname == 1 && ! empty($data['nickname'])) {
                $data['is_star']     = 0;
                $data['is_nickname'] = 1;
                $data['nickname']    = strlen($data['nickname']) > 1 ? maskDatas($data['nickname'], 0, 3) : "******";
            }
            $data['is_email'] = 0;
            if ($privacy->email == 1 && ! empty($data['email'])) {
                $data['is_star']  = 0;
                $data['is_email'] = 1;
                $data['email']    = "******";
            }
            $data['is_address'] = 0;
            if ($privacy->address == 1 && ! empty($data['address'])) {
                $data['is_star']    = 0;
                $data['is_address'] = 1;
                $data['address']    = "******";
            }
            $data['is_wechat'] = 0;
            if ($privacy->wechat == 1 && ! empty($data['wechat'])) {
                $data['is_star']   = 0;
                $data['is_wechat'] = 1;
                $data['wechat']    = "******";
            }
        }

        return $request->kernel->success($data);
    }

    /**
     * Notes: 交换名片
     *
     * @Author: 玄尘
     * @Date: 2025/4/11 15:15
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     * @throws \Throwable
     */
    public function exchange(Request $request)
    {
        $request->kernel->validate([
            'from_business_id' => 'required',
            'to_business_id'   => 'required',
        ], [
            'from_business_id.required' => '请选择要交换的名片',
            'to_business_id.required'   => '请选择要交换的名片',
        ]);

        $user             = $request->kernel->user();
        $from_business_id = $request->from_business_id;
        $to_business_id   = $request->to_business_id;

        $fromBusiness = Business::find($from_business_id);

        $this->checkBusiness($fromBusiness, $user, '名片不存在', '只能交换自己的名片');

        $toBusiness = Business::find($to_business_id);
        if ($toBusiness->user->is($user)) {
            throw new ValidatorException('不能交换自己的名片');
        }

        $this->checkBusiness($toBusiness);

        return DB::transaction(function () use ($fromBusiness, $toBusiness, $request) {
            try {
                $exists = BusinessContact::where('from_business_id', $fromBusiness->id)
                    ->where('to_business_id', $toBusiness->id)
                    ->exists();
                if ($exists) {
                    throw new ValidatorException('您已经交换过此名片');
                }
                BusinessContact::create([
                    'user_id'          => $fromBusiness->user_id,
                    'from_business_id' => $fromBusiness->id,
                    'to_business_id'   => $toBusiness->id,
                    'destination_id'   => $toBusiness->user_id,
                    'status'           => 0,
                    'is_initiator'     => 1,
                ]);
                return $request->kernel->success(true);
            } catch (ValidatorException $e) {
                throw new ValidatorException($e->getMessage());
            }
        });
    }

    /**
     * Notes: 名片当天访问情况
     *
     * @Author: 玄尘
     * @Date: 2025/4/9 17:04
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function businessInfo(Request $request)
    {
        $user = $request->kernel->user();
        //当天时间戳
        $start_time = Carbon::now()->startOfDay();
        $end_time   = Carbon::now()->endOfDay();

        $count = BusinessBrowseRecord::getMyBusinessBrowseCount($user, $start_time, $end_time);

        return $request->kernel->success($count);
    }

    public function visitorMy(Request $request)
    {
        $request->kernel->validate([
            'business_id' => ['nullable', 'integer', 'exists:jz_company_business,id'],
        ], [
            'business_id.integer' => '名片id必须是数字',
            'business_id.exists'  => '名片不存在'
        ]);

        $user        = $request->kernel->user();
        $business_id = $request->business_id ?? '';

        $page     = $request->page ?? 1;
        $pageSize = $request->pagesize ?? 10;

        if ($business_id) {
            $viewBusiness = Business::find($business_id);
            $this->checkBusiness($viewBusiness, $user, '名片不存在', '您没有权限查看此名片');
        }

        $browseRecords = BusinessBrowseRecord::query()
            ->with(['user.info', 'user.realName', 'user.jzBusiness', 'business'])
            ->when($business_id, function ($query) use ($business_id) {
                $query->whereIn('business_id', $business_id);
            }, function ($query) use ($user) {
                $query->whereHas('business', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                });
            })
            ->latest('id')
            ->paginate($pageSize);

        return $request->kernel->success(new RecordMyCollection($browseRecords));
    }

    /**
     * Notes: 访问我的
     *
     * @Author: 玄尘
     * @Date: 2024/12/25 10:14
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function myVisitor(Request $request)
    {
        $user = $request->kernel->user();

        $page     = $request->page ?? 1;
        $pageSize = $request->pagesize ?? 10;

        $browseRecords = BusinessBrowseRecord::OfUser($user)
            ->with(['business', 'realname'])
            ->whereHas('business')
            ->latest('id')
            ->paginate($pageSize);

        return $request->kernel->success(new MyRecordCollection($browseRecords));
    }

    public function qrcode(Request $request)
    {
        $this->checkBusinessById($request);

        $user        = $request->kernel->user();
        $business_id = $request->business_id;

        $business = Business::find($business_id);
        if (! $business) {
            throw new ValidatorException('名片不存在');
        }

        $size       = $request->size ?? '430';
        $envVersion = $request->version ?? 'release';
        $data       = [
            'username' => $user->username,
            'nickname' => $user->info->nickname ?? '',
            'avatar'   => $user->info->avatar_url ?? '',
        ];

        try {
            $app  = app('wechat.mini');
            $name = md5('business'.$business_id).'.png';
            $path = "share/business/{$envVersion}/{$name}";
            if (! Storage::has($path)) {
                $response = $app->getClient()->postJson('/wxa/getwxacodeunlimit', [
                    'scene'       => http_build_query([
                        'business_id' => $business_id
                    ]),
                    'page'        => 'pages/out/card',
                    'width'       => $size,
                    'is_hyaline'  => true,
                    'env_version' => $envVersion,
                    'check_path'  => false,
                ]);

                if ($response->getStatusCode() == 200) {
                    $content = $response->getContent();
                    $content = json_decode($content, true);
                    if (isset($content['errcode'])) {
                        throw new ValidatorException('获取失败');
//                    throw new ValidatorException($content['errmsg']);
                    }
                    Storage::put($path, $response->getContent());
                } else {
                    throw new ValidatorException('获取失败');
                }
            }

            $data = array_merge($data, [
                'qrcode_url' => Storage::url($path),
                'qrcode'     => "data:image/png;base64,".base64_encode(Storage::get($path))
            ]);
            return $request->kernel->success($data);
        } catch (Exception $e) {
            throw new ValidatorException($e->getMessage());
        }
    }

}
