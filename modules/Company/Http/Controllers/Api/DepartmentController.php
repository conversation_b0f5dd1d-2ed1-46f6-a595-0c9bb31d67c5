<?php

namespace Modules\Company\Http\Controllers\Api;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Company\Http\Requests\Department\AddDepartmentRequest;
use Modules\Company\Http\Requests\Department\UpdateDepartmentRequest;
use Modules\Company\Models\DepartmentRole;
use Modules\Company\Models\Role;
use Modules\Company\Models\Staff;
use Modules\User\Http\Resources\DepartmentCollection;
use Modules\User\Http\Resources\DepartmentResource;
use Modules\User\Models\Department;

class DepartmentController extends ApiController
{
    public function index(Request $request): JsonResponse
    {
        $user      = $request->kernel->user();
        $company   = $user->company;
        $parent_id = $request->parent_id ?? 0;
        if (! $company) {
            throw new \Exception('您没有权限');
        }
        $list = Department::ofEnabled()
            ->with(['company'])
            ->withCount(['children'])
            ->where('company_id', $company->id)
            ->where('parent_id', $parent_id)
            ->ordered()
            ->paginate();

        return $request->kernel->success(new DepartmentCollection($list));
    }

    public function show(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'department_id' => 'required|integer|exists:user_departments,id'
        ], [
            'department_id.required' => '缺少部门id',
            'department_id.exists'   => '部门不存在',
            'department_id.integer'  => '部门id必须是数字'
        ]);

        $department = Department::find($request->department_id);
        if (! $department->status) {
            return $this->failed('部门不存在', 404);
        }

        return $this->success(new DepartmentResource($department));
    }

    /**
     * Notes: 添加部门
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 09:47
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request): JsonResponse
    {
        $requestRule = new AddDepartmentRequest();

        $request->kernel->validate($requestRule->rules(), $requestRule->messages());

        $user = $request->kernel->user();

        if (! $user->isManagementRole($request->company_id)) {
            throw new ValidatorException('您没有权限');
        }

        DB::transaction(function () use ($request) {
            $department = Department::create([
                'name'                => $request->name,
                'remark'              => $request->description ?? '',
                'company_id'          => $request->company_id,
                'parent_id'           => $request->parent_id ?? 0,
                'is_business'         => $request->is_business,
                'is_assistant'        => $request->is_assistant,
                'is_update_knowledge' => $request->is_update_knowledge,
                'is_watermark'        => $request->is_watermark,
                'is_download'         => $request->is_download,
                'is_password'         => $request->is_password,
                'allow_user'          => 1,
                'status'              => 1,
            ]);

            $manager_id           = $request->manager_id;
            $manage_knowledge_ids = $request->manage_knowledge_ids;
            if ($manager_id) {
                $manager = Staff::find($manager_id);
                if (! $manager) {
                    throw new ValidatorException('未找到主管员工');
                }
                $managerRole = Role::where('label', Role::LABEL_MANAGER)->first();
                if (! $managerRole) {
                    throw new ValidatorException('缺少主管这个角色');
                }
                DepartmentRole::updateOrCreate([
                    'user_id'          => $manager->user_id,
                    'company_staff_id' => $manager->id,
                    'company_role_id'  => $managerRole->id,
                    'department_id'    => $department->id,
                    'company_id'       => $department->company_id,
                ]);
            }

            if ($manage_knowledge_ids) {
                $manageKnowledgeIds = explode(',', $manage_knowledge_ids);
                $staffs             = Staff::query()
                    ->whereIn('id', $manageKnowledgeIds)
                    ->get();
                $knowledgeRole      = Role::where('label', Role::LABEL_KNOWLEDGE)->first();
                if (! $knowledgeRole) {
                    throw new ValidatorException('缺少知识库管理员这个角色');
                }

                foreach ($staffs as $staff) {
                    DepartmentRole::updateOrCreate([
                        'user_id'          => $staff->user_id,
                        'company_staff_id' => $staff->id,
                        'company_role_id'  => $knowledgeRole->id,
                        'department_id'    => $department->id,
                        'company_id'       => $department->company_id,
                    ]);
                }
            }
        });

        return $this->success('添加成功');
    }

    public function update(Request $request): JsonResponse
    {
        $updateDepartmentRule = new UpdateDepartmentRequest();
        $request->kernel->validate($updateDepartmentRule->rules(), $updateDepartmentRule->messages());

        $user         = $request->kernel->user();
        $departmentId = $request->input('department_id');

        $department = Department::find($departmentId);
        if (! $department) {
            throw new ValidatorException('部门不存在');
        }

        if (! $user->isManagementRole($department->company_id)) {
            throw new ValidatorException('您没有权限');
        }

        DB::transaction(function () use ($request, $department, $user) {
            $data = [
                'name'                => $request->name,
                'parent_id'           => $request->input('parent_id', 0),
                'is_business'         => $request->input('is_business', 0),
                'is_assistant'        => $request->input('is_assistant', 0),
                'is_update_knowledge' => $request->input('is_update_knowledge', 0),
                'is_watermark'        => $request->input('is_watermark', 0),
                'is_download'         => $request->input('is_download', 0),
                'is_password'         => $request->input('is_password', 0),
            ];
            if ($request->description) {
                $data['remark'] = $request->description;
            }

            $department->update($data);

            $manager_id = $request->manager_id;
            if ($manager_id) {
                $manager = Staff::find($manager_id);
                if (! $manager) {
                    throw new ValidatorException('未找到主管员工');
                }
                $managerRole = Role::where('label', Role::LABEL_MANAGER)->first();
                if (! $managerRole) {
                    throw new ValidatorException('缺少主管这个角色');
                }

                DepartmentRole::updateOrCreate([
                    'company_role_id' => $managerRole->id,
                    'department_id'   => $department->id,
                    'company_id'      => $department->company_id,
                ], [
                    'user_id'          => $manager->user_id,
                    'company_staff_id' => $manager->id,
                ]);
            }
            $knowledge_ids = $request->knowledge_ids;

            if ($knowledge_ids) {
                $knowledgeIds  = explode(',', $knowledge_ids);
                $knowledges    = Staff::query()
                    ->whereIn('id', $knowledgeIds)
                    ->get();
                $knowledgeRole = Role::where('label', Role::LABEL_KNOWLEDGE)->first();
                if (! $knowledgeRole) {
                    throw new ValidatorException('缺少知识库管理员这个角色');
                }

                DepartmentRole::query()
                    ->where('department_id', $department->id)
                    ->where('company_role_id', $knowledgeRole->id)
                    ->delete();

                foreach ($knowledges as $knowledge) {
                    DepartmentRole::updateOrCreate([
                        'user_id'          => $knowledge->user_id,
                        'company_staff_id' => $knowledge->id,
                        'company_role_id'  => $knowledgeRole->id,
                        'department_id'    => $department->id,
                        'company_id'       => $department->company_id,
                    ]);
                }
            }
        });

        return $request->kernel->success('修改成功');
    }

    public function delete(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'department_id' => 'required|integer',
        ], [
            'department_id.required' => '部门名称必须填写',
        ]);

        $user = $request->kernel->user();

        $departmentId = $request->input('department_id');

        $department = Department::withCount(['users'])->find($departmentId);
        if (! $department) {
            throw new ValidatorException('部门不存在');
        }

        if (! $user->isManagementRole($department->company_id)) {
            throw new ValidatorException('您没有权限');
        }

        if ($department->users_count > 0) {
            throw new ValidatorException('部门有人不可删除');
        }

        $department->delete();

        return $request->kernel->success('删除成功');
    }

}