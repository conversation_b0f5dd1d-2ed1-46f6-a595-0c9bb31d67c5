<?php

namespace Modules\Company\Http\Controllers\Admin;

use App\Admin\Traits\WithUploads;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Company\Models\Company;
use Modules\Company\Models\CompanyInfo;

class IndexController extends AdminController
{
    use WithUploads;

    protected string $title = '企业';

    public function grid(): Grid
    {
        return Grid::make(Company::class, function (Grid $grid) {
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '企业名称');
                $filter->between('created_at', '创建时间')->datetime();
            });

            $grid->model()
                ->with(['staffs', 'info', 'certification', 'departmentRoles'])
                ->withCount(['staffs', 'departments'])
                ->latest('created_at');

            $grid->column('id', 'ID')->sortable();
            $grid->column('logo', 'LOGO')->image('', 50, 50);
            $grid->column('name', '企业名称');
            $grid->column('certification.code', '社会统一信用代码');
            $grid->column('certification.name', '法人姓名');
            $grid->column('certification.id_card', '法人身份证');
            $grid->column('certification.license', '营业执照')->thumb(32);
            $grid->column('info.code', '邀请码');
            $grid->column('info.exp_time', '邀请码到期时间');
            $grid->column('staffs_count', '员工数')
                ->link(fn() => admin_url('jz_companies/users').'?company_id='.$this->id);

            $grid->column('departments_count', '部门数')
                ->link(fn() => admin_url('jz_companies/departments').'?company_id='.$this->id);
            $grid->column('status', '状态')
                ->using(Company::STATUS_MAP)
                ->label(Company::STATUS_MAP_LABEL);

            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Company::with(['staffs', 'info', 'certification']), function (Form $form) {
            $form->ignore([
                'company_zu',
                'company_video',
                'company_img',
                'business_zu',
                'business_img',
                'business_video',
            ]);
            $form->block(7, function (Form\BlockForm $form) {
                $form->text('name', '名称')->required();
                $form->select('user_id', '隶属用户')
                    ->options(function ($userId) {
                        if ($userId) {
                            return [$userId => User::find($userId)->showName];
                        } else {
                            return [];
                        }
                    })
                    ->ajax(route('admin.user.users.ajax'))
                    ->required();
                $this->cover($form, 'logo', 'LOGO')->width(4);
                $form->textarea('description', '简介');
            });
            $form->block(5, function (Form\BlockForm $form) {
                $form->text('certification.name', '法人姓名')->required();
                $form->text('certification.id_card', '法人身份证')->required();
                $form->text('certification.code', '组织机构代码证')->required();
                $this->cover($form, 'certification.license', '营业执照')->width(4);
            });
            $form->block(12, function (Form\BlockForm $form) {
                $form->text('info.code', '邀请码');
                $form->datetime('info.exp_time', '邀请码时间');
                $form->radio('info.company_type', '企业介绍类型')
                    ->options(CompanyInfo::TYPE_MAP)
                    ->default(CompanyInfo::TYPE_IMG)
                    ->when(CompanyInfo::TYPE_IMG, function (Form\BlockForm $form) {
                        $images = $this->getCompanyIntroContent($form, CompanyInfo::TYPE_IMG);
                        $this->pictures($form, 'company_img', '企业介绍')->value($images);
                    })
                    ->when(CompanyInfo::TYPE_ZU, function (Form\BlockForm $form) {
                        $zu = $this->getCompanyIntroContent($form, CompanyInfo::TYPE_ZU);
                        $form->editor('company_zu', '企业介绍')->value($zu);
                    })
                    ->when(CompanyInfo::TYPE_VIDEO, function (Form\BlockForm $form) {
                        $videos = $this->getCompanyIntroContent($form, CompanyInfo::TYPE_VIDEO);
                        $this->videos($form, 'company_video', '企业介绍')->value($videos);
                    });
                $form->radio('info.business_type', '业务介绍类型')
                    ->options(CompanyInfo::TYPE_MAP)
                    ->default(CompanyInfo::TYPE_IMG)
                    ->when(CompanyInfo::TYPE_IMG, function (Form\BlockForm $form) {
                        $images = $this->getBusinessIntroContent($form, CompanyInfo::TYPE_IMG);
                        $this->pictures($form, 'business_img', '业务介绍')->value($images);
                    })
                    ->when(CompanyInfo::TYPE_ZU, function (Form\BlockForm $form) {
                        $zu = $this->getBusinessIntroContent($form, CompanyInfo::TYPE_ZU);
                        $form->editor('business_zu', '业务介绍')->value($zu);
                    })
                    ->when(CompanyInfo::TYPE_VIDEO, function (Form\BlockForm $form) {
                        $videos = $this->getBusinessIntroContent($form, CompanyInfo::TYPE_VIDEO);
                        $this->videos($form, 'business_video', '业务介绍')->value($videos);
                    });
                $form->radio('status', '状态')
                    ->options(Company::STATUS_MAP)
                    ->default(Company::STATUS_PASS);
                $form->hidden('info.company');
                $form->hidden('info.business');
                $form->showFooter();
            });
            $form->saving(function (Form $form) {
                // 根据类型添加 company 字段
                $info = request()->info;
                switch ($info['company_type']) {
                    case CompanyInfo::TYPE_ZU:
                        $info['company'] = request()->company_zu;
                        break;
                    case CompanyInfo::TYPE_IMG:
                        $info['company'] = json_encode(explode(',', request()->company_img));
                        break;
                    case CompanyInfo::TYPE_VIDEO:
                        $info['company'] = request()->company_video;
                        break;
                }

                switch ($info['business_type']) {
                    case CompanyInfo::TYPE_ZU:
                        $info['business'] = request()->business_zu;
                        break;
                    case CompanyInfo::TYPE_IMG:
                        $info['business'] = json_encode(explode(',', request()->business_img));
                        break;
                    case CompanyInfo::TYPE_VIDEO:
                        $info['business'] = request()->business_video;
                        break;
                }
                $form->info = $info;
            });
        });
    }

    private function getCompanyIntroContent(Form\BlockForm $form, string $type)
    {
        if (! $form->isEditing()) {
            return '';
        }
        $company = $form->model();

        return $company->info->company_type == $type
            ? ($type == CompanyInfo::TYPE_ZU ? $company->info->company : $company->info->getCompany('system'))
            : '';
    }

    private function getBusinessIntroContent(Form\BlockForm $form, string $type)
    {
        if (! $form->isEditing()) {
            return '';
        }
        $company = $form->model();

        return $company->info->business_type == $type
            ? ($type == CompanyInfo::TYPE_ZU ? $company->info->business : $company->info->getBusiness('system'))
            : '';
    }

}