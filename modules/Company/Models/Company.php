<?php

namespace Modules\Company\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Company\Events\CompanyCreated;
use Modules\Company\Models\Traits\BelongsToIndustry;
use Modules\Company\Models\Traits\CompanyAttribute;
use Modules\User\Models\Department;

class Company extends Model
{
    use BelongsToUser,
        BelongsToIndustry,
        HasCovers,
        CompanyAttribute,
        HasEasyStatus,
        SoftDeletes;

    public $table = 'jz_companies';

    const STATUS_UNCHECK = 0;
    const STATUS_PASS    = 1;
    const STATUS_REJECT  = 2;
    const STATUS_CLOSE   = 3;

    protected $dispatchesEvents = [
        'created' => CompanyCreated::class,
    ];

    const STATUS_MAP = [
        self::STATUS_UNCHECK => '待审核',
        self::STATUS_PASS    => '审核通过',
        self::STATUS_REJECT  => '驳回',
        self::STATUS_CLOSE   => '关闭',
    ];

    const  STATUS_MAP_LABEL = [
        self::STATUS_UNCHECK => 'info',
        self::STATUS_PASS    => 'pink',
        self::STATUS_REJECT  => 'warning',
        self::STATUS_CLOSE   => 'error',
    ];

    protected $casts = [
        'status' => 'integer'
    ];

    /**
     * Notes: 关联认证
     *
     * @Author: 玄尘
     * @Date: 2025/4/11 13:21
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function certification(): hasOne
    {
        return $this->hasOne(Certification::class);
    }

    /**
     * Notes: 关联详情
     *
     * @Author: 玄尘
     * @Date: 2025/4/11 13:21
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function info(): hasOne
    {
        return $this->hasOne(CompanyInfo::class);
    }

    /**
     * Notes   : 企业员工
     *
     * @Date   : 2021/8/2 16:57
     * <AUTHOR> Mr.wang
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function staffs(): hasMany
    {
        return $this->hasMany(Staff::class);
    }

    public function businesses(): HasMany
    {
        return $this->hasMany(Business::class);
    }

    /**
     * Notes: 关联部门
     *
     * @Author: 玄尘
     * @Date: 2025/4/11 13:21
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Relations\HasMany|\Modules\Company\Models\Company
     */
    public function departments()
    {
        return $this->hasMany(Department::class);
    }

    /**
     * Notes: 是否认证成功
     *
     * @Author: 玄尘
     * @Date: 2024/12/24 15:23
     * @return bool
     */
    public function isCertification(): bool
    {
        return $this->certification()->where('status', 1)->exists();
    }

    /**
     * Notes: 获取企业logo
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 09:39
     * @return string
     */
    public function getLogoUrlAttribute()
    {
        $logo     = $this->logo;
        $imageUrl = $logo;

        if (filter_var($logo, FILTER_VALIDATE_URL)) {
            $thumbPath = parse_url($logo, PHP_URL_PATH);  // 仅获取路径部分
            $thumb     = Str::beforeLast($thumbPath, '.').'-thumb.'.Str::afterLast($thumbPath, '.');
        } else {
            $thumb = Str::beforeLast($logo, '.').'-thumb.'.Str::afterLast($logo, '.');
        }
        if (Storage::exists($thumb)) {
            // 如果缩略图存在，使用缩略图路径
            $imageUrl = $thumb;
        }

        return $this->parseImageUrl($imageUrl);
    }

    /**
     * Notes: 关联角色中间表
     *
     * @Author: 玄尘
     * @Date: 2025/4/11 13:22
     */
    public function departmentRoles(): HasMany
    {
        return $this->hasMany(DepartmentRole::class);
    }

}