<?php

namespace Modules\Company\Models\Traits;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Modules\Company\Models\Business;
use Modules\Company\Models\BusinessContact;
use Modules\Company\Models\Company;
use Modules\Company\Models\DepartmentRole;
use Modules\Company\Models\Role;
use Modules\Company\Models\Staff;

trait HasCompanyModuleRelation
{

    /**
     * Notes: 我所属的公司
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 15:06
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function company(): HasOneThrough
    {
        return $this->hasOneThrough(
            Company::class,
            Staff::class,
            'user_id',
            'id',
            'id',
            'company_id'
        );
    }

    /**
     * Notes: 关联职位
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 15:10
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function companyRoles(): HasManyThrough
    {
        return $this->hasManyThrough(
            Role::class,
            DepartmentRole::class,
            'user_id',
            'id',
            'id',
            'company_role_id'
        );
    }

    /**
     * Notes: 关联名片
     *
     * @Author: 玄尘
     * @Date: 2024/12/25 09:11
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function jzBusinesses(): HasMany
    {
        return $this->hasMany(Business::class, 'user_id');
    }

    public function jzBusiness(): HasOne
    {
        return $this->hasOne(Business::class, 'user_id');
    }

    /**
     * Notes: 关联员工
     *
     * @Author: 玄尘
     * @Date: 2024/12/25 09:12
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function jzStaffs(): HasMany
    {
        return $this->hasMany(Staff::class);
    }

    /**
     * Notes: 交换我的
     *
     * @Author: 玄尘
     * @Date: 2024/12/25 09:13
     */
    public function jzContactMy(): HasMany
    {
        return $this->hasMany(BusinessContact::class, 'destination_id');
    }

    /**
     * Notes: 我交换的
     *
     * @Author: 玄尘
     * @Date: 2024/12/25 09:14
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function jzMyContact(): HasMany
    {
        return $this->hasMany(BusinessContact::class, 'user_id');
    }

    public function isMyCompany($company_id)
    {
        return Staff::where('user_id', $this->id)->where('company_id', $company_id)->exists();
    }

}