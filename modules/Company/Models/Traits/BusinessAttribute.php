<?php

namespace Modules\Company\Models\Traits;

use Illuminate\Database\Eloquent\Casts\Attribute;

trait BusinessAttribute
{

    public function getCan($user)
    {
        return [
            'edit'     => $this->user_id == $user->id,
            'delete'   => $this->user_id == $user->id,
            'copy'     => $this->user_id == $user->id,
            'exchange' => $this->user_id == $user->id,
        ];
    }

    public function isManage()
    {
        if ($this->isWork()) {
            return $this->user->isManagementRole($this->company_id);
        }
        return false;
    }

    public function isWork(): bool
    {
        return $this->staff()->where('status', 1)->exists();
    }

    public function isCompany(): bool
    {
        return $this->companyModel()
            ->whereHas('certification', function ($query) {
                $query->where('status', 1);
            })
            ->exists();
    }

    public function getCompany()
    {
        $company = $this->company;
        if (! $company) {
            return null;
        }
        if ($this->company_type != 0) {
            return json_decode($this->company);
        } else {
            return $company;
        }
    }

    public function getBusiness()
    {
        $business = $this->business;
        if (! $business) {
            return null;
        }
        if ($this->business_type != 0) {
            return json_decode($this->business);
        }

        return $business;
    }

    public function getAvatarUrlAttribute()
    {
        return $this->parseImageUrl($this->atavar);
    }

    protected function showName(): Attribute
    {
        return Attribute::get(
            fn($value) => sprintf(
                '[%s] %s %s',
                $this->id,
                $this->nickname,
                $this->position
            )
        );
    }
}