<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Company\Models\DepartmentRole;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create($this->getTableName(), function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->comment('企业id')->index();
            $table->unsignedBigInteger('user_id')->comment('用户id')->index();
            $table->unsignedBigInteger('department_id')->default(0)->comment('部门id')->index();
            $table->unsignedBigInteger('company_role_id')->default(0)->comment('权限id')->index();
            $table->timestamps();
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists($this->getTableName());
    }

    public function getTableName(): string
    {
        return (new DepartmentRole())->getTable();
    }
};
