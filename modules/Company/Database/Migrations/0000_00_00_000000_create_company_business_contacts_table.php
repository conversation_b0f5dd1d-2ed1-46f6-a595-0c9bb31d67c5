<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Company\Models\BusinessContact;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create($this->getTableName(), function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger("user_id")
                ->comment('发起用户ID')
                ->index();
            $table->unsignedBigInteger('destination_id')
                ->comment('目标用户ID')
                ->index();
            $table->unsignedBigInteger('from_business_id')
                ->comment('发起人名片ID')
                ->index();
            $table->unsignedBigInteger('to_business_id')
                ->comment('接收人名片ID')
                ->index();
            $table->tinyInteger('is_initiator')->default(0)->comment('1为发起人');
            $table->tinyInteger('status')->comment('0:待同意 1同意');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists($this->getTableName());
    }

    public function getTableName(): string
    {
        return (new BusinessContact())->getTable();
    }
};
