<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Company\Models\Certification;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create($this->getTableName(), function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->index();
            $table->string('name', 32)->comment('法人姓名');
            $table->string('id_card', 32)->comment('法人身份证号');
            $table->string('code')->comment('组织机构代码证');
            $table->string('license')->comment('营业执照');
            $table->json('source')
                ->nullable()
                ->comment('认证返回信息');
            $table->boolean('status')->default(0);
            $table->timestamps();
            $table->softDeletes();
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists($this->getTableName());
    }

    public function getTableName(): string
    {
        return (new Certification())->getTable();
    }
};
