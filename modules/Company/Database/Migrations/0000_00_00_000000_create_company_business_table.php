<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Company\Models\Business;
use Modules\Company\Models\BusinessStyle;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create($this->getTableName(), function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->nullable()->index();
            $table->unsignedBigInteger('user_id')->index();
            $table->unsignedBigInteger('industry_id')->nullable()->comment('行业id')->index();
            $table->integer('send_count')->default(0)->comment('发送名片次数');
            $table->string('nickname', 100)->comment('姓名');
            $table->string('company_name')->comment('企业名称');
            $table->string('company_icon')->comment('公司图标');
            $table->string('avatar')->nullable()->comment('头像');
            $table->string('phone', 20)->comment('手机号码');
            $table->string('wechat', 100)->nullable()->comment('微信');
            $table->string('email', 100)->nullable()->comment('邮箱');
            $table->string('address')->nullable()->comment('地址');
            $table->text('introduction')->nullable()->comment('个人介绍');
            $table->text('annex')->nullable()->comment('附件');
            $table->string('annex_name')->nullable()->comment('附件名称');
            $table->text('business')->nullable()->comment('业务介绍');
            $table->tinyInteger('business_type')->default(0)->comment('0:文字介绍 1：图片 2：视频');
            $table->text('company')->nullable()->comment('企业介绍');
            $table->tinyInteger('company_type')->default(0)->comment('0:文字介绍 1：图片 2：视频');
            $table->string('position')->comment('职位');
            $table->string('wiki')->nullable()->comment('wiki地址');
            $table->tinyInteger('is_default')->default(0)->comment('0:正常 1：默认');
            $table->tinyInteger('is_private')->default(0)->comment('0私有 1企业');
            $table->string('province')->nullable()->comment('省');
            $table->string('city')->nullable()->comment('市');
            $table->string('area')->nullable()->comment('区');
            $table->string('lat', 50)->nullable();
            $table->string('lng', 50)->nullable();
            $table->timestamp('deleted_at')->nullable();
            $table->timestamps();
        });

        Schema::create($this->getStyleTableName(), function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('business_id')->index();
            // 显示控制
            $table->tinyInteger('avatar_show')->default(0)->comment('0:显示头像 1不显示');
            $table->tinyInteger('nickname_show')->default(0)->comment('0显示 1不显示');
            $table->tinyInteger('phone_show')->default(0)->comment('0显示 1不显示');
            $table->tinyInteger('company_name_show')->default(0);
            $table->tinyInteger('position_show')->default(0);
            $table->tinyInteger('company_icon_show')->default(0)->comment('0显示 1不显示');
            $table->tinyInteger('email_show')->default(0)->comment('0显示 1不显示');
            $table->tinyInteger('address_show')->default(0)->comment('0显示 1不显示');

            // 资源文件
            $table->text('mini_qrcode')->nullable()->comment('小程序二维码');
            $table->text('image')->nullable()->comment('形象照');
            $table->string('company_icon')->nullable()->comment('公司图标');

            // 样式设置
            $table->string('layout')->nullable()->comment('布局');
            $table->string('background')->nullable()->comment('背景');

            // 字体样式
            $table->integer('nickname_size')->nullable()->comment('名称字体');
            $table->tinyInteger('nickname_bold')->default(0)->comment('0正常 1加粗');
            $table->string('nickname_color', 50)->nullable()->comment('名称颜色');

            $table->integer('phone_size')->nullable();
            $table->tinyInteger('phone_bold')->default(0);
            $table->string('phone_color', 10)->nullable();

            $table->integer('company_name_size')->nullable();
            $table->tinyInteger('company_name_bold')->default(0);
            $table->string('company_name_color', 50)->nullable();

            $table->integer('position_size')->nullable();
            $table->tinyInteger('position_bold')->default(0);
            $table->string('position_color', 50)->nullable();

            $table->integer('email_size')->nullable();
            $table->tinyInteger('email_bold')->default(0);
            $table->string('email_color', 50)->nullable();

            $table->integer('address_size')->nullable();
            $table->tinyInteger('address_bold')->default(0);
            $table->string('address_color', 50)->nullable();

            $table->integer('wiki_size')->nullable();
            $table->tinyInteger('wiki_bold')->default(0);
            $table->string('wiki_color', 50)->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists($this->getStyleTableName());
        Schema::dropIfExists($this->getTableName());
    }

    public function getTableName(): string
    {
        return (new Business())->getTable();
    }

    public function getStyleTableName(): string
    {
        return (new BusinessStyle())->getTable();
    }
};