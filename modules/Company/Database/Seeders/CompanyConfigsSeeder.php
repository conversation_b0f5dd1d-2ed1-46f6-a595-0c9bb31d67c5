<?php

namespace Modules\Company\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Company\Models\Industry;
use Modules\Company\Models\Role;

class CompanyConfigsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->insertRoleData();
        $this->insertIndustries();
    }

    public function insertIndustries(): true
    {
        $data = [
            [
                'title'  => '软件开发',
                'status' => 1,
            ],
            [
                'title'  => '人工智能',
                'status' => 1,
            ],
            [
                'title'  => '大数据',
                'status' => 1,
            ],
            [
                'title'  => '云计算',
                'status' => 1,
            ],
            [
                'title'  => '网络安全',
                'status' => 1,
            ],
            [
                'title'  => '投资银行',
                'status' => 1,
            ],
            [
                'title'  => '风险投资',
                'status' => 1,
            ],
            [
                'title'  => '金融科技',
                'status' => 1,
            ],
            [
                'title'  => '保险',
                'status' => 1,
            ],
            [
                'title'  => '证券',
                'status' => 1,
            ],
            [
                'title'  => 'K12教育',
                'status' => 1,
            ],
            [
                'title'  => '在线教育',
                'status' => 1,
            ],
            [
                'title'  => '语言培训',
                'status' => 1,
            ],
            [
                'title'  => '职业技能培训',
                'status' => 1,
            ],
            [
                'title'  => '高等教育',
                'status' => 1,
            ],
            [
                'title'  => '生物技术',
                'status' => 1,
            ],
            [
                'title'  => '制药',
                'status' => 1,
            ],
            [
                'title'  => '医疗器械',
                'status' => 1,
            ],
            [
                'title'  => '医院管理',
                'status' => 1,
            ],
            [
                'title'  => '健康管理',
                'status' => 1,
            ],
            [
                'title'  => '汽车制造',
                'status' => 1,
            ],
            [
                'title'  => '电子制造',
                'status' => 1,
            ],
            [
                'title'  => '机械制造',
                'status' => 1,
            ],
            [
                'title'  => '化工制造',
                'status' => 1,
            ],
            [
                'title'  => '纺织制造',
                'status' => 1,
            ],
            [
                'title'  => '电子商务',
                'status' => 1,
            ],
            [
                'title'  => '连锁零售',
                'status' => 1,
            ],
            [
                'title'  => '跨境电商',
                'status' => 1,
            ],
            [
                'title'  => '新零售',
                'status' => 1,
            ],
            [
                'title'  => '便利店',
                'status' => 1,
            ],
            [
                'title'  => '房地产开发',
                'status' => 1,
            ],
            [
                'title'  => '建筑设计',
                'status' => 1,
            ],
            [
                'title'  => '物业管理',
                'status' => 1,
            ],
            [
                'title'  => '建筑装饰',
                'status' => 1,
            ],
            [
                'title'  => '房地产中介',
                'status' => 1,
            ],
            [
                'title'  => '数字营销',
                'status' => 1,
            ],
            [
                'title'  => '广告策划',
                'status' => 1,
            ],
            [
                'title'  => '新媒体',
                'status' => 1,
            ],
            [
                'title'  => '内容创作',
                'status' => 1,
            ],
            [
                'title'  => '公关',
                'status' => 1,
            ],
            [
                'title'  => '在线旅游',
                'status' => 1,
            ],
            [
                'title'  => '酒店管理',
                'status' => 1,
            ],
            [
                'title'  => '旅行社',
                'status' => 1,
            ],
            [
                'title'  => '民宿',
                'status' => 1,
            ],
            [
                'title'  => '航空服务',
                'status' => 1,
            ],
            [
                'title'  => '农业科技',
                'status' => 1,
            ],
            [
                'title'  => '食品加工',
                'status' => 1,
            ],
            [
                'title'  => '畜牧业',
                'status' => 1,
            ],
            [
                'title'  => '渔业',
                'status' => 1,
            ],
            [
                'title'  => '农产品销售',
                'status' => 1,
            ],
            [
                'title'  => '快递物流',
                'status' => 1,
            ],
            [
                'title'  => '货运代理',
                'status' => 1,
            ],
            [
                'title'  => '冷链物流',
                'status' => 1,
            ],
            [
                'title'  => '仓储管理',
                'status' => 1,
            ],
            [
                'title'  => '供应链管理',
                'status' => 1,
            ],
            [
                'title'  => '可再生能源',
                'status' => 1,
            ],
            [
                'title'  => '石油天然气',
                'status' => 1,
            ],
            [
                'title'  => '环保技术',
                'status' => 1,
            ],
            [
                'title'  => '清洁能源',
                'status' => 1,
            ],
            [
                'title'  => '节能服务',
                'status' => 1,
            ],
        ];

        foreach ($data as $industry) {
            Industry::create($industry);
        }

        return true;
    }

    public function insertRoleData(): true
    {
        $roles = [
            [
                'name'        => '超级管理员',
                'label'       => 'administrator',
                'description' => '最高权限',
            ],
            [
                'name'        => '子管理员',
                'label'       => 'sub_administrator',
                'description' => '基本和超级管理员一样',
            ],
            [
                'name'        => '部门管理员',
                'label'       => 'manager',
                'description' => '部门的管理员',
            ],
            [
                'name'        => '知识库管理员',
                'label'       => 'knowledge',
                'description' => '知识库的管理员',
            ],
        ];
        foreach ($roles as $role) {
            Role::create($role);
        }
        return true;
    }
}
