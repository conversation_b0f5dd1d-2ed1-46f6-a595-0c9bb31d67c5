<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Modules\Company\Http\Controllers\Admin\BusinessController;
use Modules\Company\Http\Controllers\Admin\ContactController;
use Modules\Company\Http\Controllers\Admin\DepartmentController;
use Modules\Company\Http\Controllers\Admin\IndexController;
use Modules\Company\Http\Controllers\Admin\IndustryController;
use Modules\Company\Http\Controllers\Admin\RoleController;
use Modules\Company\Http\Controllers\Admin\StaffController;

Route::group([
    'prefix' => 'jz_companies',
    'as'     => 'company.',
], function (Router $router) {
    $router->resource('industries', IndustryController::class);
    $router->resource('users', StaffController::class);
    $router->resource('roles', RoleController::class);
    $router->resource('departments', DepartmentController::class);
    $router->resource('businesses', BusinessController::class);
    $router->resource('contacts', ContactController::class);
    $router->get('ajax', 'AjaxController@index');
});

Route::resource('jz_companies', IndexController::class);
