<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Modules\Company\Http\Controllers\Api\BusinessController;
use Modules\Company\Http\Controllers\Api\CardController;
use Modules\Company\Http\Controllers\Api\CompanyController;
use Modules\Company\Http\Controllers\Api\DepartmentController;
use Modules\Company\Http\Controllers\Api\DepartmentStaffController;
use Modules\Company\Http\Controllers\Api\IndustryController;
use Modules\Company\Http\Controllers\Api\RoleController;
use Modules\Company\Http\Controllers\Api\StaffController;

Route::group([
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('unverifiedCompany', [CompanyController::class, 'unverifiedCompany'])
        ->name('jz_company.unverifiedCompany');
    $router->post('authentication', [CompanyController::class, 'authentication'])->name('jz_company.authentication');
    $router->post('setCode', [CompanyController::class, 'setCode'])->name('jz_company.setCode');
    $router->post('workAuth', [CompanyController::class, 'workAuthentication'])->name('jz_company.workAuthentication');
    $router->post('companies', [CompanyController::class, 'companies'])->name('jz_company.index');
    $router->post('info', [CompanyController::class, 'info'])->name('jz_company.show');
    $router->post('update', [CompanyController::class, 'update'])->name('jz_company.update');
    $router->post('qrcode', [CompanyController::class, 'qrcode'])->name('jz_company.qrcode');
    $router->post('transference', [CompanyController::class, 'transference'])->name('jz_company.transference');
});

Route::group([
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('index', [IndustryController::class, 'index'])->name('jz_company.industries');
});

//员工
Route::group([
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('staffs', [StaffController::class, 'staffs'])->name('jz_company.staff.list');
    $router->post('searchUser', [StaffController::class, 'searchUser'])->name('jz_company.staff.search');
    $router->post('updateOpen', [StaffController::class, 'updateOpen'])->name('jz_company.staff.open_network');
    $router->post('leaveWork', [StaffController::class, 'leaveWork'])->name('jz_company.staff.leaveWork');
    $router->post('addStaff', [StaffController::class, 'addStaff'])->name('jz_company.staff.add');
    $router->post('batchAdd', [StaffController::class, 'batchAdd'])->name('jz_company.staff.batchAdd');
    $router->post('checkUser', [StaffController::class, 'checkUser'])->name('jz_company.staff.audit');
    $router->post('userInfo', [StaffController::class, 'userInfo'])->name('jz_company.staff.detail');
});

//名片
Route::group([
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'businesses',
], function (Router $router) {
    $router->post('my', [BusinessController::class, 'my'])->name('jz_business.myBusiness');
    $router->post('create', [BusinessController::class, 'create'])->name('jz_business.addBusiness');
    $router->post('delete', [BusinessController::class, 'delete'])->name('jz_business.delBusiness');
    $router->post('switch', [BusinessController::class, 'switch'])->name('jz_business.switchBusiness');
    $router->post('copy', [BusinessController::class, 'copy'])->name('jz_business.copyBusiness');
    $router->post('detail', [BusinessController::class, 'detail'])->name('jz_business.detailBusiness');
    $router->post('send', [BusinessController::class, 'send'])->name('jz_business.sendBusiness');
    $router->post('browse', [BusinessController::class, 'browse'])->name('jz_business.browseBusiness');
    $router->post('exchange', [BusinessController::class, 'exchange'])->name('jz_business.exchangeBusiness');
    $router->post('businessInfo', [BusinessController::class, 'businessInfo'])->name('jz_business.businessData');
    $router->post('visitorMy', [BusinessController::class, 'visitorMy'])->name('jz_business.visitorMyBusiness');
    $router->post('myVisitor', [BusinessController::class, 'myVisitor'])->name('jz_business.myVisitorBusiness');
    $router->post('qrcode', [BusinessController::class, 'qrcode'])->name('jz_business.generateQrCode');
});

//组织
Route::group([
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'departments'
], function (Router $router) {
    $router->post('', [DepartmentController::class, 'index'])->name('jz_departments.index');
    $router->post('show', [DepartmentController::class, 'show'])->name('jz_departments.show');
    $router->post('create', [DepartmentController::class, 'create'])->name('jz_departments.create');
    $router->post('update', [DepartmentController::class, 'update'])->name('jz_departments.update');
    $router->post('delete', [DepartmentController::class, 'delete'])->name('jz_departments.delete');

    //角色
    $router->post('roles', [RoleController::class, 'index'])->name('jz_departments.roles');
    $router->post('roles/join', [RoleController::class, 'join'])->name('jz_departments.roles.join');
    $router->post('roles/leave', [RoleController::class, 'leave'])->name('jz_departments.roles.leave');
    //员工
    $router->post('staff/departments', [DepartmentStaffController::class, 'departments'])
        ->name('jz_departments.staff.departments');
    $router->post('staff/join', [DepartmentStaffController::class, 'join'])->name('jz_departments.staff.join');
    $router->post('staff/remove', [DepartmentStaffController::class, 'remove'])->name('jz_departments.staff.remove');
});

Route::group([
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'cards'
], function (Router $router) {
    $router->post('', [CardController::class, 'rank'])->name('jz_card.rank');
    $router->post('index', [CardController::class, 'index'])->name('jz_card.my');
    $router->post('exchange', [CardController::class, 'exchange'])->name('jz_card.exchange');
    $router->post('agree', [CardController::class, 'agree'])->name('jz_card.agree_exchange');
});