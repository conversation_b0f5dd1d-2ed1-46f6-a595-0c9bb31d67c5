<?php

namespace Modules\Company\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{

    /**
     * The module namespace to assume when generating URLs to actions.
     *
     * @var string
     */
    protected string $moduleNamespace = 'Modules\Company\Http\Controllers';

    /**
     * @var string $moduleName
     */
    protected string $moduleName = 'Company';

    /**
     * Called before routes are registered.
     * Register any model bindings or pattern based filters.
     *
     * @return void
     */
    public function boot(): void
    {
        parent::boot();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map(): void
    {
        $this->mapApiRoutes();
        $this->mapAdminRoutes();
    }

    protected function mapApiRoutes(): void
    {
        Route::domain(config('api.route.domain'))
            ->namespace($this->moduleNamespace.'\\Api')
            ->prefix(config('api.route.prefix').'/jz_companies')
            ->group(module_path($this->moduleName, 'Routes/api.php'));
    }

    protected function mapAdminRoutes(): void
    {
        Route::as(config('admin.route.as'))
            ->domain(config('admin.route.domain'))
            ->middleware(config('admin.route.middleware'))
            ->namespace($this->moduleNamespace.'\\Admin')
            ->prefix(config('admin.route.prefix'))
            ->group(module_path($this->moduleName, 'Routes/admin.php'));
    }

}
