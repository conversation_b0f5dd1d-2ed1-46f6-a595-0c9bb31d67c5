<?php

namespace Modules\User\Enums;

use App\Traits\EnumMethods;

enum CertificationStatus: string
{
    use EnumMethods;

    case INIT = 'init';
    case PASS = 'pass';
    case FAILED = 'failed';

    const STATUS_MAP = [
        self::INIT->value   => '未认证',
        self::PASS->value   => '已通过',
        self::FAILED->value => '认证失败',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}