<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Support\Facades\Storage;
use Modules\User\Enums\Gender;
use Modules\User\Events\UserInfoUpdated;

class UserInfo extends Model
{
    use BelongsToUser,
        Cachable,
        HasCovers;

    protected $primaryKey = 'user_id';

    protected $casts = [
        'gender'   => Gender::class,
        'birthday' => 'date:Y-m-d',
    ];

    protected $dispatchesEvents = [
        'updated' => UserInfoUpdated::class,
    ];

    /**
     * Notes   : 获取头像地址
     *
     * @Date   : 2023/6/5 10:12
     * <AUTHOR> <Jason.C>
     * @return string
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return $this->parseImageUrl($this->avatar);
        }

        if (config('custom.default_avatar')) {
            return $this->parseImageUrl(config('custom.default_avatar'));
        }
        return Storage::url('/avatar/174.jpg');
    }

    /**
     * Notes   : 保存性别的时候，格式化数据
     *
     * @Date   : 2023/9/15 14:16
     * <AUTHOR> <Jason.C>
     * @param $value
     */
    public function setGenderAttribute($value): void
    {
        if (! is_null($value)) {
            $gender = match ($value) {
                '男', 'M', '1' => Gender::MALE,
                '女', 'F', '2' => Gender::FEMALE,
                default => Gender::SECRET,
            };

            $this->attributes['gender'] = $gender;
        }
    }

    public function getOriginNameAttribute(): string
    {
        return $this->attributes['nickname'] ?: $this->user->username;
    }

    public function getNicknameAttribute(): string
    {
        $user     = request()->kernel?->guestUser();
        $nickname = $this->attributes['nickname'] ?: $this->user->username;
        if ($user) {
            $value = $user->userOptions()->target($this->user_id)->value('nickname');
            if ($value) {
                $nickname = $value;
            }
        }
        return $nickname;
    }
}
