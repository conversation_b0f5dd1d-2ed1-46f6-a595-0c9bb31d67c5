<?php

namespace Modules\User\Http\Resources;

use App\Http\Resources\BaseCollection;
use Illuminate\Http\Request;

class DepartmentJoinCollection extends BaseCollection
{
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return new DepartmentJoinResource($item);
            }),
            'page' => $this->page(),
        ];
    }
}