<?php

namespace Modules\User\Http\Actions\User;

use App\Models\User;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;

class BatchUnLock extends BatchAction
{
    protected string $title = '批量解锁';

    public function handle(): Response
    {
        $key = $this->getKey();

        User::whereIn('id', $key)->update(['is_lock' => false]);

        return $this->response()->success('批量解锁用户成功')->refresh();
    }

    public function confirm(): array
    {
        return [
            '批量解锁',
            '确定要批量解锁用户么？',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator() || $user->can(str_replace('\\', '_', get_called_class()));
    }
}