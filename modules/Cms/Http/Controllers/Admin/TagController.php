<?php

namespace Modules\Cms\Http\Controllers\Admin;

use App\Admin\Actions\Restore;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Cms\Models\Tag;

class TagController extends AdminController
{
    protected string $title = '标签管理';

    public function grid(): Grid
    {
        return Grid::make(Tag::withCount('contents')->latest(), function (Grid $grid) {
            $grid->showBatchDelete();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(Tag::class));
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed();

                $filter->like('name', '标签名称');
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->quickSearch(['name'])
                ->placeholder('标签名称');

            $grid->column('id');
            $grid->column('name', '标签名称');
            $grid->column('contents_count', '内容数量');
            $grid->column('created_at', '创建时间');
        });
    }

    public function form(): Form
    {
        return Form::make(Tag::class, function (Form $form) {
            $form->text('name', '标签名称')
                ->required();
        });
    }

    public function ajax(Request $request): LengthAwarePaginator
    {
        $q = $request->get('q');

        return Tag::where('name', 'like', "%$q%")->paginate(null, ['id', 'name as text']);
    }
}
