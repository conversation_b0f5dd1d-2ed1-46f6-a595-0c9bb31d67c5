<?php

namespace Modules\Cms\Http\Controllers\Api;

use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Cms\Http\Resources\PageCollection;
use Modules\Cms\Http\Resources\PageResource;
use Modules\Cms\Models\Page;

class PageController extends ApiController
{
    /**
     * Notes   : 单页列表
     *
     * @Date   : 2021/4/16 10:45 上午
     * <AUTHOR> < Jason.C >
     * @param  \Illuminate\Http\Request  $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $title    = $request->title;
        $subTitle = $request->sub_title;
        $slug     = $request->slug;

        $result = Page::ofEnabled()
            ->select(['id', 'title', 'sub_title', 'description', 'slug', 'cover', 'pictures', 'clicks', 'created_at'])
            ->when($title, function ($query) use ($title) {
                $query->where('title', 'like', "%{$title}%");
            })->when($subTitle, function ($query) use ($subTitle) {
                $query->where('sub_title', 'like', "%{$subTitle}%");
            })->when($slug, function ($query) use ($slug) {
                $query->where('slug', 'like', "%{$slug}%");
            })
            ->paginate($request->limit);

        $result = new PageCollection($result);

        return $this->success($result);
    }

    /**
     * Notes   : 单页详情
     *
     * @Date   : 2021/4/16 11:25 上午
     * <AUTHOR> < Jason.C >
     * @param  \Modules\Cms\Models\Page  $page
     * @return JsonResponse
     */
    public function show(Page $page): JsonResponse
    {
        if ($page->isDisabled()) {
            return $this->failed();
        }
        return $this->success(new PageResource($page));
    }
}
