<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\Enum;
use Modules\Payment\Enums\AccountType;

class RechargeRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'type'   => [
                'bail',
                'required',
                'enum' => new Enum(AccountType::class),
            ],
            'amount' => 'bail|required|numeric|min:10',
        ];
    }

    public function messages(): array
    {
        return [
            'type.required'   => '账户类型 必须选择',
            'type.enum'       => '账户类型 不正确',
            'amount.required' => '储值金额必须填写',
            'amount.numeric'  => '储值金额必须是数字',
            'amount.min'      => '最小储值金额:min元',
        ];
    }
}