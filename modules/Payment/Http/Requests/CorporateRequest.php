<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Modules\Payment\Rules\CorporateRule;

class CorporateRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'payment_no' => ['bail', 'required', new CorporateRule],
            'bank_no'    => 'bail|required|numeric|digits_between:16,20',
            'amount'     => 'bail|required|numeric',
            'cover'      => 'bail|required',
        ];
    }

    public function messages(): array
    {
        return [
            'payment_no.required'    => '缺少支付单号',
            'payment_no.numeric'     => '支付单号必须是数字',
            'name.required'          => '缺少打款人',
            'bank_no.required'       => '打款账户必须填写',
            'bank_no.numeric'        => '打款账户只能是数字',
            'bank_no.digits_between' => '打款账户必须在:min至:max位之间',
            'amount.required'        => '缺少打款金额',
            'amount.numeric'         => '打款金额必须是数字',
            'cover.required'         => '缺少打款凭证',
        ];
    }
}
