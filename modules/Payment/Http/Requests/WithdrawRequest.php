<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use App\Rules\IsEnabledRule;
use Modules\Payment\Rules\WithdrawAmountRule;
use Modules\Payment\Rules\WithdrawExtendsRule;

class WithdrawRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'amount'     => [
                'bail',
                'required',
                'numeric',
                new WithdrawAmountRule,
            ],
            'channel_id' => [
                'bail',
                'required',
                new IsEnabledRule('\Modules\Payment\Models\WithdrawChannel', 'id', 'status', '提现规则'),
            ],
            'extends'    => [
                'bail',
                'required',
                new WithdrawExtendsRule,
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'amount.required'     => '提现金额必须填写',
            'amount.numeric'      => '提现金额必须是数字',
            'amount.min'          => '提现金额最低为:min',
            'amount.max'          => '提现金额不能超过余额:max',
            'channel_id.required' => '提现渠道必须选择',
            'channel_id.exists'   => '提现渠道不存在',
        ];
    }
}
