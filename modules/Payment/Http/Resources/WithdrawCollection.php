<?php

namespace Modules\Payment\Http\Resources;

use App\Http\Resources\BaseCollection;
use Illuminate\Http\Request;

class WithdrawCollection extends BaseCollection
{
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return new WithdrawResource($item);
            }),
            'page' => $this->page(),
        ];
    }
}