<?php

namespace Modules\Payment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PaymentResultResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'payment_no' => $this->no,
            'amount'     => $this->amount,
            'status'     => $this->status,
            'gateway'    => $this->gateway->toString(),
            'paid_at'    => (string) $this->paid_at,
            'created_at' => (string) $this->created_at,
        ];
    }
}