<?php

namespace Modules\Payment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Payment\Enums\BalancePayStatus;

class BalancePayInfoResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'amount'  => $this->amount,
            'channel' => $this->channel,
            'balance' => $this->account->{$this->channel->value},
            'can_pay' => $this->status == BalancePayStatus::UNPAY && ($this->account->{$this->channel->value} >= $this->amount),
        ];
    }
}