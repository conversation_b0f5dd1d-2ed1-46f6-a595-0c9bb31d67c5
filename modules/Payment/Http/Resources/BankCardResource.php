<?php

namespace Modules\Payment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BankCardResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'card_id'      => $this->id,
            'bank'         => new BankResource($this->bank),
            'name'         => $this->name,
            'card_no'      => $this->card_no,
            'can_withdraw' => $this->can_withdraw,
            'can_quick'    => $this->can_quick,
            'created_at'   => (string) $this->created_at,
        ];
    }
}