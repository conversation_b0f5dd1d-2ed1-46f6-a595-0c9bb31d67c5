<?php

namespace Modules\Payment\Http\Forms;

use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Modules\Payment\Enums\WithdrawStatus;
use Modules\Payment\Models\Withdraw;

class WithdrawAuditForm extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * @throws \Modules\Payment\Exceptions\RuleException
     * @throws \Exception
     */
    public function handle(array $input): JsonResponse
    {
        extract($input);

        $withdraw = Withdraw::find($this->payload['withdraw_id']);

        $res = match ($result) {
            WithdrawStatus::PASS->value => $withdraw->pass($certificate),
            WithdrawStatus::REJECT->value => $withdraw->reject($reason),
        };

        if ($res) {
            return $this->response()->success('审核成功')->refresh();
        } else {
            return $this->response()->error('审核失败');
        }
    }

    public function form(): void
    {
        $this->radio('result', '审核结果')
            ->options(WithdrawStatus::AUDIT_MAP)
            ->required()
            ->default(WithdrawStatus::PASS->value)
            ->when(WithdrawStatus::PASS->value, function () {
                $this->textarea('certificate', '打款凭证')
                    ->rules('max:255')
                    ->help('如是线下转款，可以进行记录');
            })
            ->when(WithdrawStatus::REJECT->value, function () {
                $this->textarea('reason', '驳回原因')
                    ->rules('required_if:result,'.WithdrawStatus::REJECT->value.'|max:255');
            });

        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }
}