<?php

namespace Modules\Payment\Http\Forms;

use Carbon\Carbon;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Exception;
use Modules\Payment\Drivers\PaymentFactory;
use Modules\Payment\Models\Payment;

class VirtualPayForm extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * @throws \Throwable
     */
    public function handle(array $input): JsonResponse
    {
        try {
            $payment = Payment::find($this->payload['paymentId']);
            if ($payment->paid(Carbon::parse($input['paid_at']))) {
                $payment->virtuals()->create([
                    'user'    => Admin::user(),
                    'gateway' => $input['gateway'],
                    'channel' => $input['gateway'].'_'.$input['channel'],
                    'amount'  => $input['amount'],
                    'remark'  => $input['remark'],
                    'paid_at' => $input['paid_at'],
                ]);
                return $this->response()->success('虚拟支付成功')->refresh();
            } else {
                return $this->response()->error('虚拟支付失败');
            }
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    /**
     * @throws \Dcat\Admin\Exception\RuntimeException
     */
    public function form(): void
    {
        $this->datetime('paid_at', '订单支付时间')
            ->required();
        $this->select('gateway', '支付网关')
            ->options(PaymentFactory::gatewayNames())
            ->load('channel', route('admin.payment.channels'))
            ->required();
        $this->select('channel', '支付渠道')
            ->required();
        $this->currency('amount', '支付金额')
            ->required();
        $this->textarea('remark', '备注信息');
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }

    public function default(): array
    {
        $payment = Payment::find($this->payload['paymentId']);

        return [
            'paid_at' => now(),
            'amount'  => $payment->amount,
        ];
    }
}