<?php

namespace Modules\Payment\Http\Forms;

use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Modules\Payment\Models\Security;

class UpdateSecurityForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $security = Security::find($this->payload['security_id']);

        $security->password = $input['password'];

        if ($security->save()) {
            return $this->response()->success('支付密码重置成功')->refresh();
        } else {
            return $this->response()->error('支付密码重置失败');
        }
    }

    public function form(): void
    {
        $this->password('password', '新支付密码')
            ->rules('required|regex:/[0-9]{6}/')
            ->help('请使用6位数字')
            ->required();

        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }
}