<?php

namespace Modules\Payment\Http\Forms;

use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Modules\Payment\Enums\PaymentStatus;
use Modules\Payment\Models\Payment;

class PaymentRefundForm extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * @throws \Exception
     */
    public function handle(array $input): JsonResponse
    {
        extract($input);

        if ($amount <= 0) {
            return $this->response()->error('退款金额必须大于0');
        }

        $payment = Payment::find($this->payload['payment_id']);

        $refund = $payment->refunds()->create([
            'refundable_type' => $payment->paymentable_type,
            'refundable_id'   => $payment->paymentable_id,
            'user'            => $payment->user,
            'amount'          => $amount,
        ]);
        $payment->getAdapter()->refund($refund->no, $refund->amount);//退款
        $refund->refunded(now());
        $payment->status = PaymentStatus::REFUND;
        $payment->save();
        return $this->response()->success('退款申请成功')->refresh();
    }

    public function form(): void
    {
        $payment = Payment::find($this->payload['payment_id']);

        $this->currency('amount', '退款金额')
            ->default($payment->amount)
            ->required();
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }
}