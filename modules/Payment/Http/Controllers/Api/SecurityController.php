<?php

namespace Modules\Payment\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\Payment\Http\Requests\SecurityInitRequest;
use Modules\Payment\Http\Requests\SecurityUpdateRequest;

class SecurityController extends ApiController
{
    /**
     * Notes   : 初始化设置
     *
     * @Date   : 2023/3/27 16:58
     * <AUTHOR> <Jason.C>
     */
    public function store(SecurityInitRequest $request): JsonResponse
    {
        $user = Api::user();

        if ($user->security) {
            return $this->failed('密码已经初始化');
        }

        $user->security()->create([
            'password' => $request->safe()->password,
        ]);

        return $this->success();
    }

    /**
     * Notes   : 修改密码
     *
     * @Date   : 2023/3/27 16:58
     * <AUTHOR> <Jason.C>
     */
    public function update(SecurityUpdateRequest $request): JsonResponse
    {
        $user = Api::user();

        if (! $user->security) {
            return $this->failed('密码未曾初始化');
        }

        $user->security->password = $request->safe()->password;
        $user->security->save();

        return $this->success();
    }
}