<?php

namespace Modules\Payment\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Packages\XinHuaERP\XinHuaERP;
use EasyWeChat\Kernel\Exceptions\HttpException;
use Exception;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Modules\Cms\Models\Material;
use Modules\Mall\Http\Resources\OrderCouponResource;
use Modules\Mall\Models\Order;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Exceptions\AccountException;
use Modules\Payment\Exceptions\GatewayNotExists;
use Modules\Payment\Exceptions\InsufficientFunds;
use Modules\Payment\Exceptions\UnifyPaymentException;
use Modules\Payment\Http\Requests\PaymentRequest;
use Modules\Payment\Http\Requests\UnifyInitRequest;
use Modules\Payment\Http\Requests\UnifyPaymentRequest;
use Modules\Payment\Http\Requests\UnifyQueryRequest;
use Modules\Payment\Http\Resources\PaymentResultResource;
use Modules\Payment\Models\Combine;
use Modules\Payment\Models\Payment;
use Yansongda\Artful\Exception\InvalidConfigException;

class PaymentController extends ApiController
{
    /**
     * Notes   : 创建支付订单
     *
     * @Date   : 2023/5/23 11:15
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Http\Requests\PaymentRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(PaymentRequest $request): JsonResponse
    {
        $decrypted   = Crypt::decryptString($request->paymentable);
        $target      = explode('|', $decrypted);
        $class       = Relation::getMorphedModel($target[0]);
        $paymentable = $class::find($target[1]);
        $params      = $paymentable->getPaymentParams($request->gateway, $request->gateway.'_'.$request->channel);

        return $this->success($params);
    }

    public function unifyInit(UnifyInitRequest $request): JsonResponse
    {
        try {
            $user         = Api::user();
            $orders       = $this->getOrders($request->order_class, $request->order_ids);
            $total        = 0;
            $count        = 0;
            $couponAmount = 0;
            $coupons      = collect();
            foreach ($orders as $order) {
                if ($order->user_id != $user->id) {
                    throw new UnifyPaymentException("订单[{$order->no}]非当前用户订单。");
                }

                if ($request->order_class == 'order' && $order->store->is_self) {
                    foreach ($order->items as $item) {
                        $stock = $item->sku->stock;
                        if (! in_array($item->ext['supplier_no'], [
                            'SCWX',
                            'JCJF',
                        ])) {
                            try {
                                $resData = XinHuaERP::mall()
                                    ->getStock($item->ext['erp_code'], $item->ext['supplier_no']);
                                if (isset($resData['库存数量'])) {
                                    $stock = $resData['库存数量'];
                                    $item->goods()->update([
                                        'stocks' => $stock,
                                    ]);
                                    $item->item()->update([
                                        'stock' => $stock,
                                    ]);
                                }
                            } catch (Exception $exception) {
                            }
                            if ($stock <= 0) {
                                return $this->failed("商品售罄【{$item->ext['goods_name']}】", 8989);
                            }
                        }
                    }
                }
                $total        = bcadd($total, $order->getTotalAmount(), 2);
                $count        = bcadd($count, $order->getItemCont(), 0);
                $couponAmount = bcadd($couponAmount, $order->getCouponAmount(), 2);
                if ($order->coupon) {
                    $coupons->push($order->coupon);
                }
            }
            return $this->success([
                'has_password'  => (bool) $user->security,
                'balance'       => $user->erpBalance->getBalance(true),
                'count'         => $count,
                'coupon_amount' => $couponAmount,
                'coupons'       => OrderCouponResource::collection($coupons),
                'total'         => $total,
                'number'        => $orders->count(),
                'order_nos'     => $orders->pluck('no')->toArray(),
            ]);
        } catch (UnifyPaymentException $exception) {
            return $this->failed($exception->getMessage());
        } catch (Exception $exception) {
            info('----------查询订单失败-----------');
            info($exception);
            return $this->failed('查询订单失败', 8989);
        }
    }

    protected function getOrders(string $orderClass, array $orderIds)
    {
        $model = Relation::getMorphedModel($orderClass);
        if (! class_exists($model)) {
            throw new UnifyPaymentException('订单类不存在');
        }
        $model  = new $model;
        $orders = $model->whereIn($model->getKeyName(), $orderIds)->get();
        if ($orders->count() != count($orderIds)) {
            throw new UnifyPaymentException('订单数量不匹配');
        }
        return $orders;
    }

    public function unifyPay(UnifyPaymentRequest $request)
    {
        try {
            $user   = Api::user();
            $orders = $this->getOrders($request->order_class, $request->order_ids);
//            if ($request->gateway == Gateway::BALANCE->value && ! $user->security->verify($request->security)) {
//                return $this->failed('支付密码错误');
//            }
            $noUserOrder = $orders->where('user_id', '!=', $user->id)->first();
            if ($noUserOrder) {
                throw new UnifyPaymentException("订单[{$noUserOrder->no}]非当前用户订单。");
            }
            $openId = $request->open_id;
            if (! $openId && $request->channel == Channel::WECHAT_MINI->value) {
                $code     = $request->code;
                $miniApp  = app('wechat.mini');
                $utils    = $miniApp->getUtils();
                $response = $utils->codeToSession($code);
                request()->merge(['open_id' => $response['openid']]);
            }

            if ($orders->count() > 1) {
                $paymentModel = Combine::createPayment($user, $orders, $request->gateway, $request->channel);
                $params       = $paymentModel->getPaymentParams();
                if ($paymentModel->gateway == Gateway::BALANCE) {
                    foreach ($paymentModel->payments as $payment) {
                        $balance = $payment->balances()->orderByDesc('id')->first();
                        $balance && $balance->pay();
                    }
                } else {
                    $params = [
                        'pay_params' => $params,
                        'pay_no'     => $paymentModel->no,
                        'payment_no' => $paymentModel->no,
                        'amount'     => $paymentModel->amount,
                        'channel'    => $request->channel,
                    ];
                }
            } else {
                $order        = $orders->first();
                $paymentModel = Payment::createPayment($user, $order, $request->gateway, $request->channel);
                $params       = $paymentModel->getPaymentParams();
                if ($paymentModel->gateway == Gateway::BALANCE) {
                    $balance = $paymentModel->balances()->orderByDesc('id')->first();
                    $balance && $balance->pay();
                } else {
                    $params = [
                        'pay_params' => $params,
                        'pay_no'     => $paymentModel->no,
                        'payment_no' => $paymentModel->no,
                        'amount'     => $paymentModel->amount,
                        'channel'    => $request->channel,
                    ];
                }
            }
            return $this->success([
                'payment_class' => $paymentModel->getMorphClass(),
                'payment_id'    => $paymentModel->getKey(),
                'amount'        => $paymentModel->amount,
                'freight'       => $paymentModel->freight,
                'total'         => $paymentModel->total,
                'params'        => $params,
            ]);
        } catch (HttpException $exception) {
            return $this->failed('获取微信授权失败');
        } catch (UnifyPaymentException|InsufficientFunds|GatewayNotExists|AccountException|InvalidConfigException $exception) {
            return $this->failed($exception->getMessage().'1');
        } catch (Exception $exception) {
            info('----------开始支付-----------');
            info($exception);
            return $this->failed('查询订单失败');
        }
    }

    /**
     * Notes   : 获取可用的支付网关和通道
     *
     * @Date   : 2023/5/23 11:16
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function gateways(Request $request): JsonResponse
    {
        $platform = $request->platform ?? '';
        $user     = Api::user();
        $balance  = $user?->erpBalance->getBalance();
        $data     = match ($platform) {
            'mini' => [
                [
                    'name'    => '微信支付',
                    'cover'   => Material::find(1)->cover_url ?: '',
                    'gateway' => Gateway::WECHAT->value,
                    'channel' => Channel::WECHAT_MINI,
                ],
                [
                    'name'    => '余额支付',
                    'cover'   => Material::find(2)->cover_url ?: '',
                    'gateway' => Gateway::BALANCE->value,
                    'channel' => Channel::BALANCE_BALANCE->value,
                    'balance' => $balance ?: '0.00',
                ],
            ],
            'pc' => [
                [
                    'name'    => '微信支付',
                    'cover'   => Material::find(1)->cover_url ?: '',
                    'gateway' => Gateway::WECHAT->value,
                    'channel' => Channel::WECHAT_SCAN->value,
                ],
                [
                    'name'    => '支付宝',
                    'cover'   => Material::find(4)->cover_url ?: '',
                    'gateway' => Gateway::ALIPAY->value,
                    'channel' => Channel::ALIPAY_SCAN,
                ],
                [
                    'name'    => '余额支付',
                    'cover'   => Material::find(2)->cover_url ?: '',
                    'gateway' => Gateway::BALANCE->value,
                    'channel' => Channel::BALANCE_BALANCE,
                    'balance' => $balance ?: '0.00',
                ],
            ],
            'android', 'ios' => [
//                [
//                    'name'    => '微信支付',
//                    'cover'   => Material::find(1)->cover_url ?: '',
//                    'gateway' => Gateway::WECHAT->value,
//                    'channel' => Channel::WECHAT_APP->value,
//                ],
[
    'name'    => '支付宝',
    'cover'   => Material::find(4)->cover_url ?: '',
    'gateway' => Gateway::ALIPAY->value,
    'channel' => Channel::ALIPAY_APP,
],
[
    'name'    => '余额支付',
    'cover'   => Material::find(2)->cover_url ?: '',
    'gateway' => Gateway::BALANCE->value,
    'channel' => Channel::BALANCE_BALANCE,
    'balance' => $balance ?: '0.00',
],
            ],
            default => [],
        };
        //素材ID，微信：1，余额：2，银联：3，支付宝：4

        return $this->success($data);
    }

    /**
     * Notes   : 查询支付结果
     *
     * @Date   : 2023/5/24 12:00
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Models\Payment  $payment
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function query(Payment $payment): JsonResponse
    {
        $payment->$this->checkPermission($payment);

        return $this->success(new PaymentResultResource($payment));
    }

    public function unifyQuery(UnifyQueryRequest $request): JsonResponse
    {
        $code         = 0;
        $message      = '等待结果……';
        $paymentClass = $request->safe()->payment_class;
        $paymentId    = $request->safe()->payment_id;
        try {
            $paymentModel = new $paymentClass;
            $paymentModel = $paymentModel->find($paymentId);
            if ($paymentModel->isPaid()) {
                $code    = 1;
                $message = '支付成功';
            }
        } catch (Exception $exception) {
            info('-------------订单统一查询错误-----------');
            info($exception);
        }
        return $this->success([
            'code'    => $code,
            'message' => $message,
        ]);
    }
}
