<?php

namespace Modules\Payment\Http\Controllers\Api;

use App\Http\Controllers\ApiController;
use App\Models\ReservationOrder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\Payment\Drivers\PaymentAdapters\AlipayAdapter;
use Modules\Payment\Drivers\PaymentAdapters\LakalaAdapter;
use Modules\Payment\Drivers\PaymentAdapters\PaypalAdapter;
use Modules\Payment\Drivers\PaymentAdapters\WechatAdapter;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Enums\RefundStatus;
use Modules\Payment\Models\Payment;
use Modules\Payment\Models\Refund;
use Psr\Http\Message\ResponseInterface;

class NotifyController extends ApiController
{
    protected Request $request;

    protected Gateway $gateway;

    /**
     * Notes   : 解析交易回调并处理相应的订单
     *
     * 已完成的 : 1.微信，2.拉卡拉
     *
     * @Date   : 2023/4/27 17:36
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $gateway
     * @return \Psr\Http\Message\ResponseInterface|\Illuminate\Http\JsonResponse
     * @throws \Exception|\Throwable
     */
    public function index(Request $request, string $gateway): ResponseInterface|JsonResponse
    {
        $gate = Gateway::tryFrom($gateway);
        if ($gate == null) {
            return $this->failed('Handle Error');
        }
        $this->request = $request;
        $this->gateway = $gate;
        return $this->{Str::camel($gate->value)}();
    }

    public function balance()
    {
    }

    public function chinaUms()
    {
    }

    public function jdQuick()
    {
    }

    public function unipay()
    {
    }

    public function huifu()
    {
    }

    /**
     * Notes   : 拉卡拉支付成功回调
     *
     * @Date   : 2023/7/25 17:55
     * <AUTHOR> <Jason.C>
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \Doctrine\DBAL\Exception
     * @throws \Throwable
     */
    public function lakala(): ResponseInterface
    {
        $adapter  = new LakalaAdapter(null);
        $callback = $adapter->callback();

        $outTradeNo = $callback['out_trade_no'];
        $payment    = Payment::where('no', $outTradeNo)
            ->where('gateway', $this->gateway)
            ->firstOrFail();
        $payment->paid(Carbon::parse($callback['trade_time']));
        return $adapter->success();
    }

    public function paypal()
    {
        try {
            $adapter  = new PaypalAdapter(null);
            $callback = $adapter->callback();
            if ($callback['code']) {
                if ($callback['payment_status'] == 'Completed' || ($adapter->isSandBox() && $callback['payment_status'] == 'Pending')) {
                    $outTradeNo = $callback['invoice'];
                    $payment    = Payment::where('no', $outTradeNo)
                        ->where('gateway', $this->gateway)
                        ->firstOrFail();
                    $total      = $callback['payment_gross'] ?? 0;
                    if ($total != $payment->amount) {
                        Log::channel('payment')->info('PayPalNotifyError');
                        Log::channel('payment')->info('支付金额错误');
                        echo 'ERROR';
                    } else {
                        $payment->paid(now(), $callback);
                        echo 'SUCCESS';
                    }
                }
            } else {
                Log::channel('payment')->info('PayPalNotifyError');
                Log::channel('payment')->info($callback['message']);
                echo 'ERROR';
            }
        } catch (\Exception $exception) {
            Log::channel('payment')->info('PayPalNotifyError');
            Log::channel('payment')->info(request()->all());
            echo 'ERROR';
        }
    }

    /**
     * Notes   : 处理微信支付回调
     *
     * @Date   : 2023/6/6 10:11
     * <AUTHOR> <Jason.C>
     * @return \Psr\Http\Message\ResponseInterface|void
     * @throws \Doctrine\DBAL\Exception
     * @throws \Modules\Payment\Exceptions\AlreadyPaid
     * @throws \Throwable
     */
    protected function wechat()
    {
        $adapter   = new WechatAdapter(null);
        $callback  = $adapter->callback();
        $eventType = $this->request->post('event_type');
        if ($eventType == 'TRANSACTION.SUCCESS') {
            $outTradeNo = $callback['resource']['ciphertext']['out_trade_no'];
            $payment    = Payment::where('no', $outTradeNo)
                ->where('gateway', $this->gateway)
                ->firstOrFail();
            $payment->paid(Carbon::parse($callback['resource']['ciphertext']['success_time']),
                $callback['resource']['ciphertext']);
            return $adapter->success();
        } elseif ($eventType == 'REFUND.SUCCESS') {
            $outRefundNo = $callback['resource']['ciphertext']['out_refund_no'];
            $refund      = Refund::where('no', $outRefundNo)->firstOrFail();
            if ($refund->status == RefundStatus::INIT) {
                $refund->refunded(Carbon::parse($callback['resource']['ciphertext']['success_time']));
            }
            return $adapter->success();
        }
    }

    protected function alipay()
    {
        try {
            $adapter     = new AlipayAdapter(null);
            $callback    = $adapter->callback();
            $tradeStatus = $callback['trade_status'];
            if ($tradeStatus == 'TRADE_SUCCESS') {
                $outTradeNo = $callback['out_trade_no'];
                $payment    = Payment::where('no', $outTradeNo)
                    ->where('gateway', $this->gateway)
                    ->firstOrFail();
                $payment->paid(Carbon::parse($callback['gmt_payment']), $callback);
                return $adapter->success();
            }
        } catch (\Exception $exception) {
            Log::channel('payment')->info('AlipayNotifyError');
            Log::channel('payment')->info(request()->all());
        }
    }
}

