<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Http\Actions\UpdateSecurity;
use Modules\Payment\Models\Security;

class SecurityController extends AdminController
{
    protected string $title = '账户密码';

    public function grid(): Grid
    {
        return Grid::make(Security::class, function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');

                $filter->between('created_at', '创建时间')
                    ->datetime();
                $filter->between('updated_at', '更新时间')
                    ->datetime();
            });

            $grid->quickSearch(['user.username', 'user.info.nickname'])
                ->placeholder('用户名/用户昵称');

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
                $actions->disableDelete();
                $actions->append(new UpdateSecurity());
            });

            $grid->column('用户信息')
                ->display(fn() => $this->user->showName);
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }
}
