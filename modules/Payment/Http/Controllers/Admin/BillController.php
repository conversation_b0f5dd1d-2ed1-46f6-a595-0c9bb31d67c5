<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Modules\Payment\Models\Bill;

class BillController extends AdminController
{
    public function grid(string $type = ''): Grid
    {
        return Grid::make(Bill::where('type', $type)->latest(), function (Grid $grid) use ($type) {
            $grid->disableRowSelector();
            $grid->disableBatchActions();
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->between('received', '累计收款');
                $filter->between('refund', '总退款');
                $filter->between('date', '账期')
                    ->date();
                $filter->between('created_at', '生成时间')
                    ->datetime();
            });

            $grid->column('date', '账期')
                ->display(function ($date) use ($type) {
                    return match ($type) {
                        'day' => $date->format('Y年m月d日 【第z天】'),
                        'week' => $date->format('Y第W周'),
                        'month' => $date->format('Y年m月'),
                        'quarter' => $date->format('Y年第').ceil($date->format('n') / 3).'季度',
                        'year' => $date->format('Y年'),
                    };
                });
            $grid->column('received', '累计收款');
            $grid->column('refund', '总退款');
            $grid->column('created_at', '生成时间');
        });
    }

    public function day(Content $content)
    {
        return $content
            ->header('日账单')
            ->body($this->grid('day'));
    }

    public function week(Content $content)
    {
        return $content
            ->header('周账单')
            ->body($this->grid('week'));
    }

    public function month(Content $content)
    {
        return $content
            ->header('月账单')
            ->body($this->grid('month'));
    }

    public function quarter(Content $content)
    {
        return $content
            ->header('季账单')
            ->body($this->grid('quarter'));
    }

    public function year(Content $content)
    {
        return $content
            ->header('年账单')
            ->body($this->grid('year'));
    }
}
