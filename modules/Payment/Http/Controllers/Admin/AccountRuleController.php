<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Models\AccountRule;

class AccountRuleController extends AdminController
{
    protected string $title = '规则';

    public function grid(): Grid
    {
        return Grid::make(AccountRule::latest(), function (Grid $grid) {
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '规则名称');
                $filter->like('slug', '标识');
                $filter->between('trigger', '触发次数');
                $filter->between('variable', '变量');
                $filter->equal('is_frozen', '冻结')
                    ->radio([
                        0 => '否',
                        1 => '是'
                    ]);
                $filter->between('frozen_time', '冻结时长（秒）');
                $filter->equal('status', '状态')
                    ->radio([
                        0 => '否',
                        1 => '是'
                    ]);
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->is_sys) {
                    $actions->disableDelete();
                }
            });

            $grid->quickSearch(['name', 'slug'])
                ->placeholder('规则名称/标识');
            $grid->column('id', 'ID');
            $grid->column('name', '规则名称');
            $grid->column('slug', '调用标识')
                ->copyable();
            $grid->column('type', '账户类型')
                ->display(fn($type) => $type->toString());
            $grid->column('trigger', '触发次数');
            $grid->column('variable', '变量');
            $grid->column('is_frozen', '冻结')
                ->bool();
            $grid->column('frozen_time', '冻结时长（秒）');
            $grid->column('expire_time', '有效（天）');
            $grid->column('status', '状态')
                ->bool();
            $grid->column('created_at', '创建时间');
        });
    }

    public function form(): Form
    {
        return Form::make(AccountRule::class, function (Form $form) {
            $form->text('name', '规则名称')
                ->required();
            if ($form->isCreating()) {
                $form->text('slug', '调用标识')
                    ->rules('unique:Modules\Payment\Models\AccountRule')
                    ->required();
            } else {
                $form->text('slug', '调用标识')
                    ->readOnly()
                    ->required();
            }
            $form->select('type', '账户类型')
                ->required()
                ->options(AccountType::ACCOUNT_TYPE_MAP);
            $form->number('trigger', '触发次数')
                ->required()
                ->help('-1：一次性；0：不限制；>0：每日N次');
            $form->currency('variable', '变量')
                ->required()
                ->default(0)
                ->help('为0的时候，可以根据规则自行传入');
            $form->switch('is_frozen', '冻结')
                ->help('冻结的不会立即到账，需要等待解冻时间');
            $form->number('frozen_time', '冻结时长（秒）')
                ->default(0);
            $form->number('expire_time', '有效期（天）')
                ->default(0);
            $form->switch('status', '状态');
            $form->textarea('remark', '规则说明');
        });
    }

    public function ajax(Request $request): LengthAwarePaginator
    {
        $key = $request->q;

        return AccountRule::where(function ($query) use ($key) {
            $query->where('id', 'like', "%$key%")
                ->orWhere('name', 'like', "%$key%")
                ->orWhere('slug', 'like', "%$key%");
        })
            ->paginate(null, ['id', 'name as text']);
    }
}
