<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Widgets\Box;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Dropdown;
use Illuminate\Routing\Controller;
use Modules\Payment\Enums\PaymentStatus;
use Modules\Payment\Http\Controllers\Admin\Charts\PaymentAjaxBar;
use Modules\Payment\Models\Account;
use Modules\Payment\Models\Payment;

class DashboardController extends Controller
{
    public function index(Content $content)
    {
        return $content->header('支付看板')
            ->description('支付订单、收款退款、账户统计、余额统计')
            ->body(function (Row $row) {
                foreach ($this->getPaymentCount() as $name => $value) {
                    $row->column(2, function (Column $column) use ($name, $value) {
                        $column->row(Card::make($name, $value));
                    });
                }

                $row->column(12, function (Column $column) {
                    // 构建下拉菜单，当点击菜单时发起请求获取数据重新渲染图表
                    $menu = [
                        '7' => '最近7天',
                        '10' => '最近10天',
                        '30' => '最近30天',
                    ];
                    $dropdown = Dropdown::make($menu)
                        ->button(current($menu))
                        ->click()
                        ->map(function ($v, $k) {
                            // 此处设置的 data-xxx 属性会作为post数据发送到后端api
                            return "<a class='switch-bar' data-option='{$k}'>{$v}</a>";
                        });
                    $bar = PaymentAjaxBar::make()
                        ->fetching('$("#my-box").loading()') // 设置loading效果
                        ->fetched('$("#my-box").loading(false)') // 移除loading效果
                        ->click('.switch-bar'); // 设置图表点击菜单则重新发起请求，且被点击的目标元素上的 data-xxx 属性会被作为post数据发送到后端API
                    $box = Box::make('支付统计', $bar)
                        ->id('my-box') // 设置盒子的ID
                        ->tool($dropdown); // 设置下拉菜单按钮

                    $column->row($box);
                });

            });
    }

    protected function getPaymentCount(): array
    {
        $orderCount         = Payment::whereNot('status', PaymentStatus::UNPAY)->count();
        $totalWhereIn       = [PaymentStatus::PAID, PaymentStatus::PAID_PART, PaymentStatus::REFUND, PaymentStatus::REFUND_PART];
        $orderTotalAmount   = Payment::whereIn('status', $totalWhereIn)->sum('amount');
        $incomeWhereIn      = [PaymentStatus::PAID, PaymentStatus::PAID_PART];
        $orderIncomeAmount  = Payment::whereIn('status', $incomeWhereIn)->sum('amount');
        $refundWhereIn      = [PaymentStatus::REFUND, PaymentStatus::REFUND_PART];
        $orderRefundAmount  = Payment::whereIn('status', $refundWhereIn)->sum('amount');
        $accountCount       = Account::count();
        $accountAllAmount   = Account::sum('balance');

        return [
            '支付订单数' => $orderCount,
            '收款总金额' => $orderTotalAmount,
            '实收总金额' => $orderIncomeAmount,
            '退款总金额' => $orderRefundAmount,
            '账户总数量' => $accountCount,
            '账户总余额' => $accountAllAmount,
        ];
    }
}