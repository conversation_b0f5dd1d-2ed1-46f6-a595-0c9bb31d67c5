<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Database\Eloquent\Builder;
use Modules\Payment\Enums\RefundStatus;
use Modules\Payment\Http\Actions\RefundQuery;
use Modules\Payment\Models\Refund;

class RefundController extends AdminController
{
    protected string $title = '退款订单';

    public function grid(): Grid
    {
        $refund = Refund::with(['payment'])
            ->when(request()->payment_id, function (Builder $builder, $paymentId) {
                $builder->where('payment_id', $paymentId);
            })
            ->latest();

        return Grid::make($refund, function (Grid $grid) {
            $grid->disableRowSelector();
            $grid->disableCreateButton();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->status == RefundStatus::COMPLETE) {
                    $actions->append(new RefundQuery);
                }
                $actions->disableDelete();
                $actions->disableEdit();
                $actions->disableView();
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('no', '退款单号');
                $filter->like('payment.no', '支付单号');
                $filter->between('amount', '退款金额');
                $filter->equal('status', '退款状态')
                    ->select(RefundStatus::STATUS_MAP);
                $filter->between('refund_at', '退款时间')
                    ->datetime();
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->quickSearch('no', 'payment.no')
                ->placeholder('退款单号/支付单号');

            $grid->column('no', '退款单号');
            $grid->column('payment.no', '支付单号');
            $grid->column('payment.amount', '订单金额');
            $grid->column('amount', '退款金额');
            $grid->column('status', '退款状态')
                ->display(fn($status) => $status->toString())
                ->label(RefundStatus::STATUS_LABEL);
            $grid->column('refund_at', '退款时间');
            $grid->column('created_at', '创建时间');
        });
    }
}
