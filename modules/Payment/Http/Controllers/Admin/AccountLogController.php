<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Http\Actions\AccountLogSource;
use Modules\Payment\Http\Actions\UnFreezeAccountLog;
use Modules\Payment\Models\AccountLog;
use Modules\Payment\Models\AccountRule;

class AccountLogController extends AdminController
{
    protected string $title = '账变记录';

    public function grid(): Grid
    {
        return Grid::make(AccountLog::with(['account.user.info', 'rule'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableRowSelector();
            $grid->disableEditButton();
            $grid->disableDeleteButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('account.user.username', '用户名');
                $filter->like('account.user.info.nickname', '用户昵称');
                $filter->equal('rule_id', '账变规则')
                    ->select(function ($id) {
                        if ($id) {
                            $rule = AccountRule::find($id);
                            return [$rule->id => $rule->name];
                        }
                    })
                    ->ajax(route('admin.payment.rules.ajax'));
                $filter->equal('type', '账户类型')
                    ->select(AccountType::ACCOUNT_TYPE_MAP);
                $filter->between('amount', '变动金额');
                $filter->between('balance', '当期余额');
                $filter->equal('frozen', '是否冻结')
                    ->radio([
                        0 => '否',
                        1 => '是',
                    ]);
                $filter->between('unfreeze_at', '解冻时间')
                    ->datetime();
                $filter->equal('is_expired', '是否过期')
                    ->radio([
                        0 => '否',
                        1 => '是',
                    ]);
                $filter->between('expired_at', '过期时间')
                    ->datetime();
                $filter->between('created_at', '账变时间')
                    ->datetime();
            });

            $grid->quickSearch(['rule.name', 'account.user.username', 'account.user.info.nickname'])
                ->placeholder('触发规则/用户名/用户昵称');

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->frozen) {
                    $actions->append(new UnFreezeAccountLog);
                }
                $actions->append(new AccountLogSource);
            });

            $grid->column('用户账户')
                ->display(fn() => $this->account->user->showName);
            $grid->column('rule.name', '触发规则');
            $grid->column('type', '账户类型')
                ->display(fn($type) => $type->toString());
            $grid->column('描述')->display(fn() => $this->source['remark'] ?? '');
            $grid->column('amount', '变动金额');
            $grid->column('balance', '当期余额');
            $grid->column('frozen', '冻结')
                ->bool();
            $grid->column('unfreeze_at', '解冻时间');
            $grid->column('is_expired', '是否过期')
                ->bool();
            $grid->column('expired_at', '过期时间');
            $grid->column('created_at', '账变时间');
        });
    }
}
