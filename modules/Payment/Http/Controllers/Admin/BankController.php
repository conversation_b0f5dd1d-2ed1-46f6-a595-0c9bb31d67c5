<?php

namespace Modules\Payment\Http\Controllers\Admin;

use App\Admin\Actions\Batches\BatchDisable;
use App\Admin\Actions\Batches\BatchEnable;
use App\Admin\Traits\WithUploads;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Payment\Models\Bank;

class BankController extends AdminController
{
    use WithUploads;

    protected string $title = '银行列表';

    public function grid(): Grid
    {
        return Grid::make(Bank::ordered(), function (Grid $grid) {
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '银行名称');
                $filter->like('short', '缩写');
            });

            $grid->batchActions([new BatchEnable(Bank::class), new BatchDisable(Bank::class)]);

            $grid->quickSearch(['name', 'short'])
                ->placeholder('银行名称/缩写');

            $grid->column('id');
            $grid->column('cover', '银行图标')
                ->thumb(32);
            $grid->column('name', '银行名称');
            $grid->column('short', '缩写');
            $grid->column('status')
                ->bool();
            $grid->column('order', '排序')
                ->orderable();
        });
    }

    public function form(): Form
    {
        return Form::make(Bank::class, function (Form $form) {
            $form->text('name')
                ->required();
            $form->text('short')
                ->required();
            $this->cover($form);
            $form->number('order')
                ->default(0)
                ->required();
        });
    }

    public function ajax(Request $request): LengthAwarePaginator
    {
        $q = $request->get('q');

        return Bank::where('name', 'like', "%$q%")
            ->orWhere('short', 'like', "%$q%")
            ->ordered()
            ->paginate(null, ['id', 'name as text']);
    }
}
