<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Drivers\PaymentFactory;
use Modules\Payment\Enums\BalancePayStatus;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Models\Balance;

class BalanceController extends AdminController
{
    protected string $title = '余额支付';

    public function grid(): Grid
    {
        return Grid::make(Balance::with(['account.user.info', 'payment'])->latest(), function (Grid $grid) {
            $grid->disableRowSelector();
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('no', '付款凭据');
                $filter->like('account.user.username', '支付用户');
                $filter->like('account.user.info.nickname', '用户昵称');
                $filter->like('payment.no', '支付单号');
                $filter->between('amount', '支付金额');
                $filter->equal('status', '支付结果')
                    ->select(BalancePayStatus::STATUS_MAP);
                $filter->between('paid_at', '支付时间')
                    ->datetime();
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->quickSearch('no', 'payment.no', 'account.user.username', 'account.user.info.nickname')
                ->placeholder('付款凭据/支付单号/用户名/用户昵称');

            $grid->column('支付用户')
                ->display(fn() => $this->account->user->showName);
            $grid->column('no', '付款凭据')->copyable();
            $grid->column('payment.no', '支付单号')->copyable();
            $grid->column('渠道')
                ->display(fn() => PaymentFactory::channels(Gateway::BALANCE)[$this->channel->value] ?? 'UNKNOWN')
                ->label();
            $grid->column('amount', '支付金额');
            $grid->column('status', '支付结果')
                ->display(fn($status) => $status->toString())
                ->label(BalancePayStatus::STATUS_LABEL);
            $grid->column('paid_at', '支付时间');
            $grid->column('created_at', '创建时间');
        });
    }
}
