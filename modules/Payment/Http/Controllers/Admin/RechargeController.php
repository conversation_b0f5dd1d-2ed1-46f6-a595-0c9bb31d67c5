<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Enums\RechargeStatus;
use Modules\Payment\Models\Recharge;

class RechargeController extends AdminController
{
    protected string $title = '充值订单';

    public function grid(): Grid
    {
        return Grid::make(Recharge::with(['user'])->latest(), function (Grid $grid) {
            $grid->disableRowSelector();
            $grid->disableActions();
            $grid->disableCreateButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('no', '订单编号');
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->equal('type', '类型')
                    ->select(AccountType::ACCOUNT_TYPE_MAP);
                $filter->between('amount', '应付金额');
                $filter->between('receipts', '入账金额');
                $filter->equal('status', '状态')
                    ->select(RechargeStatus::STATUS_MAP);
                $filter->between('created_at', '创建时间')
                    ->datetime();
                $filter->between('updated_at', '更新时间')
                    ->datetime();
            });

            $grid->quickSearch(['no', 'user.username', 'user.info.nickname'])
                ->placeholder('订单编号/用户名/用户昵称');

            $grid->column('no', '订单编号');
            $grid->column('下单用户')
                ->display(fn() => $this->user->showName);
            $grid->column('type')
                ->display(fn($type) => $type->toString());
            $grid->column('amount', '应付金额');
            $grid->column('receipts', '入账金额');
            $grid->column('status', '状态')
                ->display(fn($status) => $status->toString())
                ->label(RechargeStatus::STATUS_LABEL);
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }
}
