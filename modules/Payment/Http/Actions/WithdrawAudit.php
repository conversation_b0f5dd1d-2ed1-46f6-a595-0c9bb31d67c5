<?php

namespace Modules\Payment\Http\Actions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\Payment\Http\Forms\WithdrawAuditForm;

class WithdrawAudit extends RowAction
{
    protected string $title = '提现审核';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(WithdrawAuditForm::make()->payload(['withdraw_id' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}