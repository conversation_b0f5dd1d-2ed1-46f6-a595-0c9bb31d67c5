<?php

namespace Modules\Payment\Http\Actions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\Payment\Http\Forms\PaymentRefundForm;

class PaymentRefund extends RowAction
{
    protected string $title = '退款';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(PaymentRefundForm::make()->payload(['payment_id' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}