<?php

namespace Modules\Payment\Http\Actions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\Payment\Http\Forms\CorporateAuditForm;

class CorporateAudit extends RowAction
{
    protected string $title = '打款审核';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(CorporateAuditForm::make()->payload(['corporate_id' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}