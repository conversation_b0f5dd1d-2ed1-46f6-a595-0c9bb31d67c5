<?php

namespace Modules\Payment\Http\Renders;

use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Table;
use Modules\Payment\Models\AccountLog;

class LogSourceRender extends LazyRenderable
{
    public function render(): string
    {
        $log = AccountLog::find($this->payload['log_id']);

        $data = [];

        foreach ($log->source as $key => $item) {
            $data[] = [
                $key,
                $item,
            ];
        }

        return Table::make(['KEY', 'VALUE'], $data);
    }
}