## GET 余额支付

GET /payment/balance/{pay_no}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pay_no|path|string| 是 |none|
|Authorization|header|string| 否 |none|
|X-Device-Id|header|string| 否 |none|
|Accept|header|string| 否 |none|
|X-Request-Time|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "string",
  "data": {
    "amount": "string",
    "type": "string",
    "balance": 0,
    "can_pay": true
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» data|object|true|none||none|
|»» amount|string|true|none||none|
|»» type|string|true|none||none|
|»» balance|number|true|none||none|
|»» can_pay|boolean|true|none||none|

## POST 支付

POST /payment/balance/{pay_no}

> Body 请求参数

```yaml
security: "111111"

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pay_no|path|string| 是 |none|
|Authorization|header|string| 否 |none|
|X-Device-Id|header|string| 否 |none|
|Accept|header|string| 否 |none|
|X-Request-Time|header|integer| 否 |none|
|body|body|object| 否 |none|
|» security|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "string",
  "data": {
    "method": "string",
    "url": "string",
    "msg": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» data|object|true|none||none|
|»» method|string|true|none||none|
|»» url|string|true|none||none|
|»» msg|string|true|none||none|

## GET 获取支付结果

GET /payment/balance/{pay_no}/result

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pay_no|path|string| 是 |none|
|Authorization|header|string| 否 |none|
|X-Device-Id|header|string| 否 |none|
|Accept|header|string| 否 |none|
|X-Request-Time|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "string",
  "data": {
    "pay_no": "string",
    "status": 0,
    "paid_at": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» data|object|true|none||none|
|»» pay_no|string|true|none||none|
|»» status|integer|true|none||none|
|»» paid_at|string|true|none||none|

