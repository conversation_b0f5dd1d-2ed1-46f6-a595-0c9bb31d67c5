<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use Exception;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Exceptions\CallbackParamsException;
use Modules\Payment\Models\Combine;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;
use Yansongda\Pay\Pay;
use Yansongda\Pay\Plugin\Wechat\V3\Pay\Refund\QueryPlugin;
use Yansongda\Pay\Provider\Alipay;

class AlipayAdapter implements PaymentAdapter
{
    protected Alipay $driver;

    public function __construct(protected Payment|Combine|null $payment)
    {
        $this->driver = Pay::alipay($this->getConfig());
    }

    public function getConfig(): array
    {
        return [
            'logger' => [
                'enable'   => config('payment.LOGGER_ENABLE'),
                'file'     => storage_path('logs/pay-alipay.log'),
                'level'    => 'debug',
                'type'     => 'daily',
                'max_file' => 30,
            ],
            'alipay' => [
                'default' => [
                    'app_id'                  => config('payment.ALIPAY_APP_ID'),
                    'app_secret_cert'         => config('payment.ALIPAY_APP_SECRET_CERT'),
                    'app_public_cert_path'    => config('payment.ALIPAY_APP_PUBLIC_CERT_PATH'),
                    'alipay_public_cert_path' => config('payment.ALIPAY_PUBLIC_CERT_PATH'),
                    'alipay_root_cert_path'   => config('payment.ALIPAY_ROOT_CERT_PATH'),
                    'return_url'              => config('payment.ALIPAY_RETURN_URL'),
                    'notify_url'              => route('api.payment.notify', Gateway::ALIPAY->value),
                ],
            ],
        ];
    }

    public function wap(): array
    {
        return [
            $this->driver
                ->web($this->prepareOrderParams(['quit_url' => '']))
                ->getBody()
                ->getContents(),
        ];
    }

    public function web(): array
    {
        return [
            $this->driver
                ->web($this->prepareOrderParams())
                ->getBody()
                ->getContents(),
        ];
    }

    protected function prepareOrderParams(array $extends = []): array
    {
        if ($this->payment->getMorphClass() == Combine::class) {
            $description = '北国书香网订单';
        } else {
            $description = $this->payment->paymentable->getTitle();
        }
        $base = [
            'out_trade_no' => $this->payment->no,
            'subject'      => $description,
            'total_amount' => $this->payment->amount,
        ];

        return array_merge($base, $extends);
    }

    public function app(): array
    {
        return [
            'params' => $this->driver
                ->app($this->prepareOrderParams())
                ->getBody()
                ->getContents(),
        ];
    }

    public function scan(): array
    {
        return $this->driver
            ->scan($this->prepareOrderParams())
            ->toArray();
    }

    public function mini(): array
    {
        return $this->driver
            ->mini($this->prepareOrderParams(['buyer_id' => '']))
            ->toArray();
    }

    /**
     * @throws \Exception
     */
    public function callback(): array
    {
        try {
            return $this->driver->callback()->toArray();
        } catch (Exception $exception) {
            throw new CallbackParamsException('回调参数解析错误');
        }
    }

    public function success(): ResponseInterface
    {
        return $this->driver->success();
    }

    public function query(): array
    {
        return [];
    }

    public function refundQuery(string $refundNo): array
    {
        $order      = [
            'out_trade_no' => $refundNo,
        ];
        $allPlugins = $this->driver
            ->mergeCommonPlugins([QueryPlugin::class]);
        return Pay::alipay()->pay($allPlugins, $order)->toArray();
    }

    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        $order = [
            'out_trade_no'  => $this->payment->no,
            'refund_amount' => (string) $refundAmount,
        ];

        return $this->driver->refund($order)->toArray();
    }
}
