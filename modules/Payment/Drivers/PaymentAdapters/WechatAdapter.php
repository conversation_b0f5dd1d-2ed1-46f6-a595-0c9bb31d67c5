<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use Exception;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Exceptions\CallbackParamsException;
use Modules\Payment\Models\Combine;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;
use Yansongda\Pay\Pay;
use Yansongda\Pay\Plugin\Wechat\V3\Pay\Refund\QueryPlugin;
use Yansongda\Pay\Provider\Wechat;

class WechatAdapter implements PaymentAdapter
{
    protected Wechat $driver;

    public function __construct(protected Payment|Combine|null $payment)
    {
        $this->driver = Pay::wechat($this->getConfig());
    }

    public function getConfig(): array
    {
        return [
            'logger' => [
                'enable'   => (bool) config('payment.LOGGER_ENABLE'),
                'file'     => storage_path('logs/pay-wechat.log'),
                'level'    => 'info',
                'type'     => 'daily',
                'max_file' => 30,
            ],
            'wechat' => [
                'default' => [
                    'mch_id'                  => config('payment.WECHAT_MCH_ID'),
                    'mch_secret_key'          => config('payment.WECHAT_MCH_SECRET_KEY'),
                    'mch_secret_cert'         => config('payment.WECHAT_MCH_SECRET_CERT'),
                    'mch_public_cert_path'    => config('payment.WECHAT_MCH_PUBLIC_CERT_PATH'),
                    'notify_url'              => route('api.payment.notify', Gateway::WECHAT->value),
                    'mp_app_id'               => config('payment.WECHAT_MP_APP_ID'),
                    'mini_app_id'             => config('payment.WECHAT_MINI_APP_ID'),
                    'app_id'                  => config('payment.WECHAT_APP_ID'),
                    'wechat_public_cert_path' => [],
                    'mode'                    => Pay::MODE_NORMAL,
                ],
            ],
        ];
    }

    /**
     * Notes   : 公众号支付.[OK]
     *
     * @Date   : 2023/3/24 17:16
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function mp(): array
    {
        $order = $this->prepareOrderParams([
            'payer' => [
                'openid' => request()->open_id,
            ],
        ]);

        return $this->driver
            ->mp($order)
            ->toArray();
    }

    protected function prepareOrderParams(array $extends = []): array
    {
        $base = [
            'out_trade_no' => $this->payment->no,
            'description'  => $this->payment->paymentable->getTitle(),
            'amount'       => [
                'total' => (int) bcmul($this->payment->amount, 100, 0),
            ],
        ];
        return array_merge($base, $extends);
    }

    /**
     * Notes   : 手机网站支付
     *
     * @Date   : 2023/3/24 17:16
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function wap(): array
    {
        $order = $this->prepareOrderParams([
            'scene_info' => [
                'payer_client_ip' => request()->ip(),
                'h5_info'         => [
                    'type' => 'Wap',
                ],
            ],
        ]);

        return $this->driver
            ->wap($order)
            ->toArray();
    }

    /**
     * Notes   : APP支付
     *
     * @Date   : 2023/3/24 17:16
     * <AUTHOR> <Jason.C>
     */
    public function app(): array
    {
        $order = $this->prepareOrderParams([]);

        return $this->driver
            ->app($order)
            ->toArray();
    }

    /**
     * Notes   : 扫码支付.[OK]
     *
     * @Date   : 2023/3/24 17:16
     * <AUTHOR> <Jason.C>
     */
    public function scan(): array
    {
        $this->driver = Pay::wechat($this->getConfig());

        $order = $this->prepareOrderParams();

        return $this->driver
            ->scan($order)
            ->toArray();
    }

    /**
     * Notes   : 小程序支付
     *
     * @Date   : 2023/3/24 17:17
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function mini(): array
    {
        $order = $this->prepareOrderParams([
            'payer' => [
                'openid' => request()->open_id,
            ],
        ]);
        return $this->driver
            ->mini($order)
            ->toArray();
    }

    /**
     * @throws \Exception
     */
    public function callback(): array
    {
        try {
            return $this->driver->callback()->toArray();
        } catch (Exception) {
            throw new CallbackParamsException('回调参数解析错误');
        }
    }

    /**
     * Notes   : 成功的回调响应
     *
     * @Date   : 2023/6/5 17:40
     * <AUTHOR> <Jason.C>
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function success(): ResponseInterface
    {
        return $this->driver->success();
    }

    /**
     * Notes   : 支付结果查询
     *
     * @Date   : 2023/6/5 17:40
     * <AUTHOR> <Jason.C>
     * @return array
     * @throws \Yansongda\Pay\Exception\ContainerException
     * @throws \Yansongda\Pay\Exception\InvalidParamsException
     * @throws \Yansongda\Pay\Exception\ServiceNotFoundException
     */
    public function query(): array
    {
        $order = [
            'out_trade_no' => $this->payment->no,
        ];

        return $this->driver->query($order)->toArray();
    }

    public function refundQuery(string $refundNo): array
    {
        $order      = [
            'out_refund_no' => $refundNo,
        ];
        $allPlugins = $this->driver
            ->mergeCommonPlugins([QueryPlugin::class]);
        return Pay::wechat()->pay($allPlugins, $order)->toArray();
    }

    /**
     * Notes   : 退款
     *
     * @Date   : 2023/6/6 09:32
     * <AUTHOR> <Jason.C>
     * @param  string  $refundNo
     * @param  float  $refundAmount
     * @param  string  $desc
     * @return array
     * @throws \Yansongda\Pay\Exception\ContainerException
     * @throws \Yansongda\Pay\Exception\InvalidParamsException
     * @throws \Yansongda\Pay\Exception\ServiceNotFoundException
     */
    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        $order = [
            'out_trade_no'  => $this->payment->no,
            'out_refund_no' => $refundNo,
            'amount'        => [
                'refund'   => (int) bcmul($refundAmount, 100, 0),
                'total'    => (int) bcmul($this->payment->amount, 100, 0),
                'currency' => 'CNY',
            ],
        ];

        return $this->driver->refund($order)->toArray();
    }
}
