<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;

class AppleAdapter implements PaymentAdapter
{
    /** @var array 配置信息 */
    private array $config;

    /** @var string 正式环境接口地址 */
    public const BASE_URI = 'https://buy.itunes.apple.com';

    /** @var string 沙箱环境接口地址 */
    public const BASE_URI_SANDBOX = 'https://sandbox.itunes.apple.com';

    /** @var int 验证成功状态码 */
    public const STATUS_VALID = 0;

    /** @var int 订阅过期状态码 */
    public const STATUS_SUBSCRIPTION_EXPIRED = 21006;

    /** @var array 错误状态码映射 */
    public const ERROR_STATUS_MAP = [
        21000 => '请求未使用 HTTP POST 方法',
        21001 => '此状态码已不再使用',
        21002 => '收据数据格式不正确或服务暂时性问题，请重试',
        21003 => '收据无法通过验证',
        21004 => '提供的共享密钥与账号文件中的不匹配',
        21005 => '收据服务器暂时无法提供收据，请重试',
        21007 => '此收据来自测试环境，但发送至正式环境验证',
        21008 => '此收据来自正式环境，但发送至测试环境验证',
        21009 => '内部数据访问错误，请稍后重试',
        21010 => '找不到用户账号或已被删除',
        21100 => '内部数据访问错误',
        21199 => '内部数据访问错误'
    ];

    /** @var string 自动续期订阅类型 */
    public const TYPE_AUTO_RENEWABLE = 'auto_renewable';

    /** @var string 非自动续期类型 */
    public const TYPE_NON_RENEWABLE = 'non_renewable';

    /**
     * @param  Payment|null  $payment  支付实例
     */
    public function __construct(protected Payment|null $payment)
    {
        $this->config = $this->getConfig();
    }

    /**
     * 获取配置信息
     *
     * @return array
     */
    public function getConfig(): array
    {
        return [
            'sandbox'         => config('payment.APPLE_CHANNEL_PAY') === 'sandbox',
            'shared_secret'   => config('payment.APPLE_IAP_SHARED_SECRET'),
            'timeout'         => 30,
            'connect_timeout' => 10
        ];
    }

    /**
     * 获取苹果支付配置
     *
     * @return array
     */
    public function apple_iap(): array
    {
        return $this->config;
    }

    /**
     * 支付回调处理
     *
     * @return array
     */
    public function callback(): array
    {
        return [];
    }

    /**
     * 成功响应
     *
     * @return ResponseInterface
     */
    public function success(): ResponseInterface
    {
        return new Response(200, [], [
            'code'    => 'success',
            'message' => '执行成功'
        ]);
    }

    /**
     * 错误响应
     *
     * @param  string  $message  错误信息
     * @return ResponseInterface
     */
    public function error(string $message): ResponseInterface
    {
        return new Response(400, [], [
            'code'    => 'error',
            'message' => $message
        ]);
    }

    /**
     * 订单查询
     *
     * @return array
     */
    public function query(): array
    {
        return [];
    }

    /**
     * 退款处理
     *
     * @param  string  $refundNo  退款单号
     * @param  float  $refundAmount  退款金额
     * @param  string  $desc  退款说明
     * @return array
     */
    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        return [];
    }

    /**
     * 验证收据
     *
     * @param  string  $receiptData  收据数据
     * @param  string  $type  订阅类型
     * @return array
     * @throws \Exception
     */
    /**
     * 验证收据
     *
     * @param  string  $receiptData  收据数据
     * @param  string  $type  订阅类型
     * @return array
     * @throws \Exception
     */
    public function verify(string $receiptData, string $type = self::TYPE_NON_RENEWABLE): array
    {
        try {
            $client = new Client([
                'base_uri'        => $this->config['sandbox'] ? self::BASE_URI_SANDBOX : self::BASE_URI,
                'timeout'         => $this->config['timeout'],
                'connect_timeout' => $this->config['connect_timeout']
            ]);

            $payload = [
                'receipt-data' => $receiptData
            ];

            // 对于自动续期订阅，需要添加密钥验证
            if ($type === self::TYPE_AUTO_RENEWABLE) {
                $payload['password']                 = $this->config['shared_secret'];
                $payload['exclude-old-transactions'] = true;
            }

            $response = $client->post('/verifyReceipt', [
                'json' => $payload
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            if (! isset($responseData['status'])) {
                throw new \Exception('验证响应格式错误');
            }

            $status = $responseData['status'];

            // 处理环境不匹配的情况
            if ($status === 21007 || $status === 21008) {
                $newBaseUri = ($status === 21007) ? self::BASE_URI_SANDBOX : self::BASE_URI;

                $client = new Client([
                    'base_uri'        => $newBaseUri,
                    'timeout'         => $this->config['timeout'],
                    'connect_timeout' => $this->config['connect_timeout']
                ]);

                $response = $client->post('/verifyReceipt', [
                    'json' => $payload
                ]);

                $responseData = json_decode($response->getBody()->getContents(), true);
                $status       = $responseData['status'];
            }

            if ($status > 0 && $status !== self::STATUS_SUBSCRIPTION_EXPIRED) {
                throw new \Exception(self::ERROR_STATUS_MAP[$status] ?? '未知错误');
            }

            return $type === self::TYPE_AUTO_RENEWABLE
                ? $this->handleAutoRenewableReceipt($responseData)
                : $this->handleNonRenewableReceipt($responseData);
        } catch (GuzzleException $e) {
            throw new \Exception('收据验证请求失败: '.$e->getMessage());
        } catch (\JsonException $e) {
            throw new \Exception('响应解析失败: '.$e->getMessage());
        }
    }

    /**
     * 处理非自动续期订阅的收据
     *
     * @param  array  $responseData
     * @return array
     * @throws \Exception
     */
    protected function handleNonRenewableReceipt(array $responseData): array
    {
        $receipt = $responseData['receipt'] ?? [];
        if (empty($receipt)) {
            throw new \Exception('收据数据为空');
        }

        $inApp = $receipt['in_app'] ?? [];
        if (empty($inApp)) {
            throw new \Exception('未找到购买记录');
        }

        // 获取最新的一笔交易记录
        $latestTransaction = end($inApp);

        // 获取支付时间（毫秒时间戳）
        $purchaseTimestamp = intval($latestTransaction['purchase_date_ms'] ?? 0);
        if ($purchaseTimestamp === 0) {
            throw new \Exception('无效的支付时间');
        }

        // 计算过期时间
        $productId = $latestTransaction['product_id'] ?? '';
        preg_match('/\.(\d+)days/', $productId, $matches);
        $days = $matches[1] ?? 30; // 默认30天

        $expiresTimestamp = $purchaseTimestamp + ($days * 24 * 60 * 60 * 1000); // 转换为毫秒
        $now              = time() * 1000;

        return [
            'status'                      => $expiresTimestamp > $now ? self::STATUS_VALID : self::STATUS_SUBSCRIPTION_EXPIRED,
            'transaction_id'              => $latestTransaction['transaction_id'] ?? '',
            'original_transaction_id'     => $latestTransaction['original_transaction_id'] ?? '',
            'product_id'                  => $productId,
            'bundle_id'                   => $receipt['bundle_id'] ?? '',
            'purchase_timestamp'          => $purchaseTimestamp,              // 毫秒时间戳
            'purchase_time'               => date('Y-m-d H:i:s', $purchaseTimestamp / 1000),  // 格式化的时间
            'expires_timestamp'           => $expiresTimestamp,               // 毫秒时间戳
            'expires_time'                => date('Y-m-d H:i:s', $expiresTimestamp / 1000),    // 格式化的时间
            'is_trial_period'             => ($latestTransaction['is_trial_period'] ?? 'false') === 'true',
            'environment'                 => $responseData['environment'] ?? 'Production',
            'receipt_creation_date'       => $receipt['receipt_creation_date'] ?? '',
            'in_app_ownership_type'       => $latestTransaction['in_app_ownership_type'] ?? '',
            // 新增支付相关信息
            'quantity'                    => $latestTransaction['quantity'] ?? '1',
            'original_purchase_timestamp' => intval($latestTransaction['original_purchase_date_ms'] ?? 0),
            'original_purchase_time'      => date('Y-m-d H:i:s',
                intval($latestTransaction['original_purchase_date_ms'] ?? 0) / 1000),
        ];
    }

    /**
     * 处理自动续期订阅的收据
     *
     * @param  array  $responseData
     * @return array
     * @throws \Exception
     */
    protected function handleAutoRenewableReceipt(array $responseData): array
    {
        // 获取最新的收据信息
        $latestReceiptInfo = $responseData['latest_receipt_info'] ?? [];

        if (empty($latestReceiptInfo)) {
            return $responseData;
        }
        #todo  以下的代码不确定
        // 如果是数组，取最新的一条记录
        if (is_array($latestReceiptInfo)) {
            $latestReceiptInfo = end($latestReceiptInfo);
        }

        // 检查订阅状态
        $expiresDate = $latestReceiptInfo['expires_date_ms'] ?? 0;
        $now         = time() * 1000; // 转换为毫秒

        if ($expiresDate < $now) {
            $responseData['status'] = self::STATUS_SUBSCRIPTION_EXPIRED;
            throw new \Exception('订阅已过期');
        }

        return [
            'status'       => self::STATUS_VALID,
            'receipt'      => $latestReceiptInfo,
            'expires_date' => date('Y-m-d H:i:s', $expiresDate / 1000),
            'environment'  => $responseData['environment'] ?? 'production'
        ];
    }
}