<?php

namespace Modules\Payment\Drivers\WithdrawAdapters;

use Modules\Payment\Contracts\WithdrawAdapter;

class AlipayAdapter implements WithdrawAdapter
{
    public function disburse(string $no, float $amount, array $options): bool
    {
        # todo 调用支付宝转账
        return false;
    }

    public static function fields(): array
    {
        return [
            'username' => [
                'name'  => '支付宝账号',
                'rules' => 'required',
            ],
            'name'     => [
                'name'  => '收款人姓名',
                'rules' => 'required',
            ],
        ];
    }

    public static function params(): array
    {
        return [
            'app_id'      => [
                'name'  => '支付宝账号',
                'rules' => 'required',
            ],
            'app_secret'  => [
                'name'  => '支付宝账号',
                'rules' => 'required',
            ],
            'public_key'  => [
                'name'  => '公钥证书',
                'rules' => 'required',
            ],
            'private_key' => [
                'name'  => '私钥证书',
                'rules' => 'required',
            ],
        ];
    }
}