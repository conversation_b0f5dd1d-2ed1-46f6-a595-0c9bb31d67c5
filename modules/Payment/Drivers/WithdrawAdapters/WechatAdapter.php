<?php

namespace Modules\Payment\Drivers\WithdrawAdapters;

use Exception;
use Modules\Payment\Contracts\WithdrawAdapter;

class WechatAdapter implements WithdrawAdapter
{
    public function disburse(string $no, float $amount, array $options): bool
    {
        throw new Exception('微信付款暂时未完成');
        # todo 调用微信支付的付款到零钱
        return false;
    }

    public static function fields(): array
    {
        return [
            'open_id' => [
                'name'  => 'OPENID',
                'rules' => 'required',
            ],
            'name'    => [
                'name'  => '收款人姓名',
                'rules' => 'required',
            ],
        ];
    }

    public static function params(): array
    {
        return [
            'app_id'      => [
                'name'  => '公众号APPID',
                'rules' => 'required',
            ],
            'mch_id'      => [
                'name'  => '商户号',
                'rules' => 'required',
            ],
            'app_secret'  => [
                'name'  => '支付密钥',
                'rules' => 'required',
            ],
            'public_key'  => [
                'name'  => '公钥证书',
                'rules' => 'required',
            ],
            'private_key' => [
                'name'  => '私钥证书',
                'rules' => 'required',
            ],
        ];
    }
}