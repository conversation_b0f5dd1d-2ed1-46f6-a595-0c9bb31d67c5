<?php

namespace Modules\Payment\Drivers;

use App\Models\Configuration;
use Illuminate\Support\Arr;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Drivers\PaymentAdapters\AlipayAdapter;
use Modules\Payment\Drivers\PaymentAdapters\AppleAdapter;
use Modules\Payment\Drivers\PaymentAdapters\AppleIapAdapter;
use Modules\Payment\Drivers\PaymentAdapters\BalanceAdapter;
use Modules\Payment\Drivers\PaymentAdapters\ChinaUmsAdapter;
use Modules\Payment\Drivers\PaymentAdapters\CorporateAdapter;
use Modules\Payment\Drivers\PaymentAdapters\DouGongAdapter;
use Modules\Payment\Drivers\PaymentAdapters\JdQuickAdapter;
use Modules\Payment\Drivers\PaymentAdapters\LakalaAdapter;
use Modules\Payment\Drivers\PaymentAdapters\PaypalAdapter;
use Modules\Payment\Drivers\PaymentAdapters\TzBankAdapter;
use Modules\Payment\Drivers\PaymentAdapters\UnipayAdapter;
use Modules\Payment\Drivers\PaymentAdapters\WechatAdapter;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Exceptions\GatewayNotExists;
use Modules\Payment\Models\Combine;
use Modules\Payment\Models\Payment;

class PaymentFactory
{
    protected Gateway $gateway;
    protected Channel $channel;

    public function __construct(
        protected Payment|Combine $payment
    ) {
        $this->gateway = $this->payment->gateway;
        $this->channel = $this->payment->channel;
    }

    public static function gatewayNames(): array
    {
        $gateways = [];

        foreach (self::gateways() as $item) {
            $gateways[$item->value] = $item->toString();
        }

        return $gateways;
    }

    /**
     * Notes   : 获取可用的支付网关
     *
     * @Date   : 2023/3/23 17:12
     * <AUTHOR> <Jason.C>
     * @return Gateway[]
     */
    public static function gateways(): array
    {
        $gateways = [];

        config('payment.GATEWAY_ENABLE_WECHAT') && $gateways[] = Gateway::WECHAT;
        config('payment.GATEWAY_ENABLE_ALIPAY') && $gateways[] = Gateway::ALIPAY;
        config('payment.GATEWAY_ENABLE_UNIPAY') && $gateways[] = Gateway::UNIPAY;
        config('payment.GATEWAY_ENABLE_CHINA_UMS') && $gateways[] = Gateway::CHINA_UMS;
        config('payment.GATEWAY_ENABLE_JD_QUICK') && $gateways[] = Gateway::JD_QUICK;
        config('payment.GATEWAY_ENABLE_BALANCE') && $gateways[] = Gateway::BALANCE;
        config('payment.GATEWAY_ENABLE_DOU_GONG') && $gateways[] = Gateway::DOU_GONG;
        config('payment.GATEWAY_ENABLE_LAKALA') && $gateways[] = Gateway::LAKALA;
        config('payment.GATEWAY_ENABLE_TZ_BANK') && $gateways[] = Gateway::TZ_BANK;
        config('payment.GATEWAY_ENABLE_CORPORATE') && $gateways[] = Gateway::CORPORATE;
        config('payment.GATEWAY_ENABLE_PAYPAL') && $gateways[] = Gateway::PAYPAL;
        config('payment.GATEWAY_ENABLE_APPLE') && $gateways[] = Gateway::APPLE;

        return $gateways;
    }

    public static function channels(Gateway $gateway): array
    {
        return match ($gateway) {
            Gateway::WECHAT => Configuration::getConfigSource('GATEWAY_CHANNEL_WECHAT', 'payment'),
            Gateway::ALIPAY => Configuration::getConfigSource('GATEWAY_CHANNEL_ALIPAY', 'payment'),
            Gateway::UNIPAY => Configuration::getConfigSource('GATEWAY_CHANNEL_UNIPAY', 'payment'),
            Gateway::JD_QUICK => Configuration::getConfigSource('GATEWAY_CHANNEL_JD_QUICK', 'payment'),
            Gateway::CHINA_UMS => Configuration::getConfigSource('GATEWAY_CHANNEL_CHINA_UMS', 'payment'),
            Gateway::BALANCE => Configuration::getConfigSource('GATEWAY_CHANNEL_BALANCE', 'payment'),
            Gateway::DOU_GONG => Configuration::getConfigSource('GATEWAY_CHANNEL_DOU_GONG', 'payment'),
            Gateway::LAKALA => Configuration::getConfigSource('GATEWAY_CHANNEL_LAKALA', 'payment'),
            Gateway::TZ_BANK => Configuration::getConfigSource('GATEWAY_CHANNEL_TZ_BANK', 'payment'),
            Gateway::CORPORATE => Configuration::getConfigSource('GATEWAY_CHANNEL_CORPORATE', 'payment'),
            Gateway::APPLE_IAP => Configuration::getConfigSource('GATEWAY_ENABLE_APPLE', 'payment'),
        };
    }

    /**
     * @throws \Exception
     */
    public function getPaymentParams(): array
    {
        $channel = $this->channel->toDriver();

        if (! in_array($this->gateway, PaymentFactory::gateways())) {
            throw new GatewayNotExists(sprintf('不支持的支付网关【%s】', $this->gateway->name));
        }
        if (! Arr::exists(PaymentFactory::enabledChannels($this->gateway), $channel)) {
            throw new GatewayNotExists(sprintf(' 不支持的支付渠道【%s】【%s】', $this->gateway->name, $channel));
        }

        if (! method_exists($this->getAdapter(), $channel)) {
            throw new GatewayNotExists(sprintf('不支持的支付渠道【%s】【%s】', $this->gateway->name, $channel));
        }

        return call_user_func([$this->getAdapter(), $channel]);
    }

    public static function enabledChannels(Gateway $gateway): array
    {
        return match ($gateway) {
            Gateway::WECHAT => self::getChannels('GATEWAY_CHANNEL_WECHAT'),
            Gateway::ALIPAY => self::getChannels('GATEWAY_CHANNEL_ALIPAY'),
            Gateway::UNIPAY => self::getChannels('GATEWAY_CHANNEL_UNIPAY'),
            Gateway::JD_QUICK => self::getChannels('GATEWAY_CHANNEL_JD_QUICK'),
            Gateway::CHINA_UMS => self::getChannels('GATEWAY_CHANNEL_CHINA_UMS'),
            Gateway::BALANCE => self::getChannels('GATEWAY_CHANNEL_BALANCE'),
            Gateway::DOU_GONG => self::getChannels('GATEWAY_CHANNEL_DOU_GONG'),
            Gateway::LAKALA => self::getChannels('GATEWAY_CHANNEL_LAKALA'),
            Gateway::TZ_BANK => self::getChannels('GATEWAY_CHANNEL_TZ_BANK'),
            Gateway::CORPORATE => self::getChannels('GATEWAY_CHANNEL_CORPORATE'),
            Gateway::PAYPAL => ['web' => 'H5支付',],
            Gateway::APPLE => self::getChannels('GATEWAY_CHANNEL_APPLE'),
        };
    }

    protected static function getChannels($key): array
    {
        $case = Configuration::getConfigSource($key, 'payment');

        return Arr::only($case, config('payment.'.$key));
    }

    /**
     * @throws \Exception
     */
    public function getAdapter(): PaymentAdapter
    {
        return match ($this->gateway) {
            Gateway::ALIPAY => new AlipayAdapter($this->payment),
            Gateway::BALANCE => new BalanceAdapter($this->payment),
            Gateway::CHINA_UMS => new ChinaUmsAdapter($this->payment),
            Gateway::JD_QUICK => new JdQuickAdapter($this->payment),
            Gateway::UNIPAY => new UnipayAdapter($this->payment),
            Gateway::WECHAT => new WechatAdapter($this->payment),
            Gateway::DOU_GONG => new DouGongAdapter($this->payment),
            Gateway::LAKALA => new LakalaAdapter($this->payment),
            Gateway::TZ_BANK => new TzBankAdapter($this->payment),
            Gateway::CORPORATE => new CorporateAdapter($this->payment),
            Gateway::PAYPAL => new PaypalAdapter($this->payment),
            Gateway::APPLE => new AppleAdapter($this->payment),
        };
    }
}