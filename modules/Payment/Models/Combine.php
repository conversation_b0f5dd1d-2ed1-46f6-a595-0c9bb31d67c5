<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Models\User;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use Modules\Mall\Factories\Order\OrderResult;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Drivers\PaymentFactory;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Enums\PaymentStatus;
use Modules\Payment\Exceptions\AccountException;
use Modules\Payment\Exceptions\AlreadyPaid;
use DateTimeInterface;

class Combine extends Model
{
    use BelongsToUser, AutoCreateOrderNo;

    public string $orderNoField  = 'order_no';
    public string $orderNoPrefix = 'COM';

    protected $casts = [
        'paid_at' => 'datetime',
        'gateway' => Gateway::class,
        'channel' => Channel::class,
        'status'  => PaymentStatus::class,
    ];
    protected $table = 'payment_combines';

    /**
     * @param  \App\Models\User  $user
     * @param   $orders
     * @param  string  $gateway
     * @param  string  $channel
     * @return mixed
     */
    public static function createPayment(User $user, $orders, string $gateway, string $channel): self
    {
        $total = 0;
        foreach ($orders as $order) {
            $total = bcadd($total, $order->getTotalAmount(), 2);
        }
        if ($channel == Channel::BALANCE_BALANCE->value) {
            $balance = $user->erpBalance->getBalance(true);
            if ($balance < $total) {
                throw new AccountException('余额不足或尚未绑定会员信息！');
            }
        }
        $combine = Combine::create([
            'user_id' => $user->id,
            'total'   => $total,
            'gateway' => $gateway,
            'channel' => $channel,
            'status'  => PaymentStatus::UNPAY,
        ]);
        foreach ($orders as $order) {
            $combine->payments()->create([
                'user_id'          => $order->user->getKey(),
                'paymentable_type' => $order->getMorphClass(),
                'paymentable_id'   => $order->getKey(),
                'gateway'          => $gateway,
                'channel'          => $channel,
                'status'           => PaymentStatus::UNPAY,
                'amount'           => $order->getTotalAmount(),
            ]);
        }
        return $combine;
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    protected static function boot(): void
    {
        parent::boot();
    }

    /**
     * 单号
     *
     * @return mixed
     */
    public function getNoAttribute()
    {
        return $this->order_no;
    }

    /**
     * 支付金额
     *
     * @return mixed
     */
    public function getAmountAttribute()
    {
        return $this->total;
    }

    /**
     * 获取支付参数
     *
     * @return array
     * @throws \Exception
     */
    public function getPaymentParams()
    {
        $factory = new PaymentFactory($this);
        return $factory->getPaymentParams();
    }

    /**
     * 是否支付成功
     *
     * @return bool
     */
    public function isPaid(): bool
    {
        if ($this->status == PaymentStatus::UNPAY) {
            $res = $this->payments()->where('status', PaymentStatus::UNPAY->value)->count() <= 0;
            return $res;
        } else {
            return true;
        }
    }

    public function paid(DateTimeInterface $paidAt, array $payJson)
    {
        if ($this->status != PaymentStatus::UNPAY) {
            throw new AlreadyPaid('订单非可支付状态');
        }
        return DB::transaction(function () use ($paidAt, $payJson) {
            $this->paid_at = $paidAt;
            $this->status  = PaymentStatus::PAID;
            if ($this->save()) {
                foreach ($this->payments as $payment) {
                    $payment->paid($paidAt, $payJson);
                }
            }
        });
    }

    /**
     * Notes   : 获取当前支付订单的支付适配器
     *
     * @Date   : 2023/6/5 17:43
     * <AUTHOR> <Jason.C>
     * @return \Modules\Payment\Contracts\PaymentAdapter
     * @throws \Exception
     */
    public function getAdapter(): PaymentAdapter
    {
        $factory = new PaymentFactory($this);
        return $factory->getAdapter();
    }
}
