<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\HasCovers;
use App\Traits\MorphToApprover;
use Dcat\Admin\Admin;
use Exception;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\DB;
use Modules\Payment\Enums\CorporateStatus;
use Modules\Payment\Models\Traits\BelongsToPayment;

class Corporate extends Model
{
    use MorphToApprover,
        BelongsToPayment,
        HasCovers;

    protected $table = 'payment_corporates';

    protected $casts = [
        'audit_at' => 'datetime',
        'source'   => 'json',
        'status'   => CorporateStatus::class,
    ];

    /**
     * Notes: 是否可以审核
     *
     * @Author: 玄尘
     * @Date: 2023/9/8 11:22
     * @return bool
     */
    public function canAudit(): bool
    {
        return $this->status == CorporateStatus::INIT;
    }

    /**
     * Notes: 订单
     *
     * @Author: 玄尘
     * @Date: 2023/10/9 15:44
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function orderable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Notes: 通过
     *
     * @Author: 玄尘
     * @Date: 2023/9/8 11:31
     * @param  string|null  $remark
     * @return bool
     * @throws \Exception
     */
    public function pass(?string $remark = null): bool
    {
        if (! $this->canAudit()) {
            throw new Exception('该记录已经被审核');
        }

        try {
            DB::transaction(function () use ($remark) {
                $this->status   = CorporateStatus::PASS;
                $this->remark   = $remark;
                $this->audit_at = now();
                $this->approver = Admin::user();
                $this->save();
                $this->payment->paid(now());
            });

            return true;
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * Notes: 驳回
     *
     * @Author: 玄尘
     * @Date: 2023/9/8 11:31
     * @param  string|null  $remark
     * @return bool
     * @throws \Exception
     */
    public function refuse(?string $remark = null): bool
    {
        if (! $this->canAudit()) {
            throw new Exception('该记录已经被审核');
        }

        try {
            DB::transaction(function () use ($remark) {
                $this->status   = CorporateStatus::REFUSE;
                $this->remark   = $remark ?? null;
                $this->audit_at = now();
                $this->approver = Admin::user();
                $this->save();
            });

            return true;
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}
