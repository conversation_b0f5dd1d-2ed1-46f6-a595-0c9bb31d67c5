<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Models\User;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Drivers\PaymentFactory;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Enums\PaymentStatus;
use Modules\Payment\Enums\RefundStatus;
use Modules\Payment\Exceptions\AlreadyPaid;

class Payment extends Model
{
    use AutoCreateOrderNo,
        BelongsToUser,
        SoftDeletes;

    protected $table = 'payments';

    protected $casts = [
        'paid_at' => 'datetime',
        'gateway' => Gateway::class,
        'channel' => Channel::class,
        'status'  => PaymentStatus::class,
    ];

    public static function createPayment(User $user, $order, string $gateway, string $channel): self
    {
        $amount = $order->getTotalAmount();
        if ($amount > 0 && config('payment.PAYMENT_TEST', 0)) {
            $amount = 0.01;
        }
        return Payment::create([
            'user_id'          => $user->getKey(),
            'paymentable_type' => $order->getMorphClass(),
            'paymentable_id'   => $order->getKey(),
            'gateway'          => $gateway,
            'channel'          => $channel,
            'status'           => PaymentStatus::UNPAY,
            'amount'           => $amount,
        ]);
    }

    public function getRouteKeyName(): string
    {
        return 'no';
    }

    /**
     * Notes   : 支付对象
     *
     * @Date   : 2023/3/23 09:45
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function paymentable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 是否支付完成
     *
     * @return bool
     */
    public function isPaid(): bool
    {
        return in_array($this->status, [
            PaymentStatus::PAID,
        ]);
    }

    /**
     * 关联合并支付
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function combine(): BelongsTo
    {
        return $this->belongsTo(Combine::class, 'combine_id');
    }

    /**
     * Notes   : 虚拟支付
     *
     * @Date   : 2023/4/3 17:32
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function virtuals(): HasMany
    {
        return $this->hasMany(Virtual::class);
    }

    /**
     * Notes   : 余额支付
     *
     * @Date   : 2023/4/3 17:32
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function balances(): HasMany
    {
        return $this->hasMany(Balance::class);
    }

    /**
     * Notes   : 关联线下打款
     *
     * @Date   : 2023/10/10 13:50
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function corporates(): HasMany
    {
        return $this->hasMany(Corporate::class);
    }

    /**
     * Notes   : 标记订单已支付状态
     *
     * @Date   : 2023/3/23 10:00
     * <AUTHOR> <Jason.C>
     * @param  \DateTimeInterface  $paidAt  支付时间
     * @param  array  $payJson
     * @return bool
     * @throws \Modules\Payment\Exceptions\AlreadyPaid
     */
    public function paid(DateTimeInterface $paidAt, array $payJson = []): bool
    {
        if ($this->status != PaymentStatus::UNPAY) {
            throw new AlreadyPaid('订单非可支付状态');
        }

        return DB::transaction(function () use ($paidAt, $payJson) {
            $this->paid_at = $paidAt;
            $this->status  = PaymentStatus::PAID;
            $res           = $this->save();
            if (method_exists($this->paymentable, 'paid')) {
                $this->paymentable->paid($this);
            }
            if ($this->gateway == Gateway::WECHAT) {
                if ($payJson['transaction_id'] ?? false) {
                    $this->successLog()->updateOrCreate([
                        'transaction_id' => $payJson['transaction_id'],
                    ], [
                        'pay_json' => $payJson,
                        'total'    => bcdiv($payJson['amount']['payer_total'] ?? 0, 100, 2),
                        'openid'   => $payJson['payer']['openid'] ?? '',
                    ]);
                } else {
                    Log::channel('payment')->info($payJson);
                }
            }
            if ($this->gateway == Gateway::ALIPAY) {
                if ($payJson['trade_no'] ?? false) {
                    $this->successLog()->updateOrCreate([
                        'transaction_id' => $payJson['trade_no'],
                    ], [
                        'pay_json' => $payJson,
                        'total'    => $payJson['total_amount'] ?? 0,
                        'openid'   => $payJson['buyer_logon_id'] ?? '',
                    ]);
                } else {
                    Log::channel('payment')->info($payJson);
                }
            }

            if ($this->gateway == Gateway::APPLE) {
                $this->successLog()
                    ->updateOrCreate([
                        'transaction_id' => $payJson['transaction_id'],
                    ], [
                        'pay_json' => $payJson,
                        'total'    => $payJson['amount'] ?? 0,
                        'openid'   => $payJson['bundle_id'] ?? '',
                    ]);
            }

            return $res;
        });
    }

    public function successLog(): HasOne
    {
        return $this->hasOne(PaymentSuccessLog::class, 'payment_id');
    }

    /**
     * Notes   : 获取当前支付订单的支付适配器
     *
     * @Date   : 2023/6/5 17:43
     * <AUTHOR> <Jason.C>
     * @return \Modules\Payment\Contracts\PaymentAdapter
     * @throws \Exception
     */
    public function getAdapter(): PaymentAdapter
    {
        $factory = new PaymentFactory($this);
        return $factory->getAdapter();
    }

    public function getPaymentParams()
    {
        $factory = new PaymentFactory($this);
        return $factory->getPaymentParams();
    }

    /**
     * Notes: 设置已退款
     *
     * @Author: 玄尘
     * @Date: 2024/4/8 15:21
     */
    public function refunded(): bool
    {
        $refundTotal = $this->getRefundTotal();

        $status = PaymentStatus::REFUND_PART;
        if ($refundTotal == $this->amount) {
            $status = PaymentStatus::REFUND;
        }

        $this->status = $status;
        $this->save();
        return true;
    }

    public function getRefundTotal()
    {
        return $this->refunds()->where('status', RefundStatus::COMPLETE)->sum('amount');
    }

    /**
     * Notes   : 退款单
     *
     * @Date   : 2023/3/23 09:46
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function refunds(): HasMany
    {
        return $this->hasMany(Refund::class);
    }

}
