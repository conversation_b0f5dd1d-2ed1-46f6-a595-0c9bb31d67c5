<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Modules\Payment\Exceptions\RuleException;
use Modules\Payment\Jobs\AccountExpired;
use Modules\Payment\Jobs\AutoUnFreeze;

class Account extends Model
{
    use BelongsToUser,
        SoftDeletes;

    protected $table = 'payment_accounts';

    /**
     * Notes   : 执行账户规则
     *
     * @Date   : 2023/5/22 13:56
     * <AUTHOR> <Jason.C>
     * @param  string|int|\Modules\Payment\Models\AccountRule  $accountRule  执行规则
     * @param  float  $amount  指定账变金额
     * @param  int|string|null  $expireTime  指定过期时间
     * @param  array  $source  标记来源
     * @return true
     * @throws \Modules\Payment\Exceptions\RuleException
     */
    public function exec(
        string|int|AccountRule $accountRule,
        float $amount = 0,
        int|string|null $expireTime = null,
        array $source = []
    ): true {
        $rule = match (true) {
            $accountRule instanceof AccountRule => $accountRule,
            is_string($accountRule) => AccountRule::where('slug', $accountRule)->firstOrFail(),
            is_int($accountRule) => AccountRule::findOrFail($accountRule),
            default => throw new RuleException('规则不正确')
        };

        if ($rule->isDisabled()) {
            throw new RuleException('规则不可用');
        }

        if ($rule->trigger == 0) {
            return $this->execute($rule, $amount ?: $rule->variable, $expireTime, $source);
        } elseif ($rule->trigger > 0) {
            $todayTimes = $this->logs()
                ->where('rule_id', $rule->getKey())
                ->whereDate('created_at', Carbon::today())
                ->count();
            if ($rule->trigger > $todayTimes) {
                return $this->execute($rule, $amount ?: $rule->variable, $expireTime, $source);
            } else {
                throw new RuleException('今日已达最大执行次数');
            }
        } else {
            if (! $this->logs()->where('rule_id', $rule->getKey())->exists()) {
                return $this->execute($rule, $amount ?: $rule->variable, $expireTime, $source);
            } else {
                throw new RuleException('已达最大执行次数');
            }
        }
    }

    /**
     * Notes   : 执行账户的增减并记录日志
     *
     * @Date   : 2023/5/22 15:21
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Models\AccountRule  $rule
     * @param  float  $amount
     * @param  int|null  $expireTime
     * @param  array  $source
     * @return true
     */
    private function execute(
        AccountRule $rule,
        float $amount,
        int|string|null $expireTime = null,
        array $source = []
    ): true {
        # 如果是扣款，立即执行
        if (is_numeric($expireTime)) {
            if ($expireTime == 0) {
                $expireTime = $rule->expire_time;
            }
            $expireDateTime = now()->addDays($expireTime)->endOfDay();
        } elseif (is_string($expireTime)) {
            $expireDateTime = Carbon::parse($expireTime)->endOfDay();
        } elseif (is_null($expireTime)) {
            $expireDateTime = $expireTime;
        }
        if ($amount < 0) {
            $this->decrement($rule->type->value, abs($amount));
            $expireDateTime = null;
        } elseif (! $rule->is_frozen) {
            # 若是非冻结的，立即执行
            $this->increment($rule->type->value, abs($amount));
        }
        $log = $this->logs()->create([
            'rule_id'     => $rule->getKey(),
            'type'        => $rule->type,
            'amount'      => $amount ?: $rule->variable,
            'balance'     => $this->{$rule->type->value},
            'frozen'      => $rule->is_frozen,
            'unfreeze_at' => $rule->is_frozen ? now()->addDays($rule->frozen_time) : null,
            'source'      => $source,
            'is_expired'  => false,
            'expired_at'  => $expireDateTime,
            'created_at'  => $source['created_at'] ?? Carbon::now(),
        ]);

        # 如果过期时间是在当天，凌晨的定时任务获取不到这条数据，会导致无法加入队列
        if ($expireTime && Carbon::now()->addSeconds($expireTime)->between(Carbon::today(), Carbon::today())) {
            AccountExpired::dispatch($log)->delay(Carbon::now()->addSeconds($expireTime - 1));
        }

        if ($rule->is_frozen) {
            AutoUnFreeze::dispatch($log)->delay($rule->frozen_time);
        }

        return true;
    }

    /**
     * Notes   : 关联账户日志
     *
     * @Date   : 2023/5/22 15:33
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function logs(): HasMany
    {
        return $this->hasMany(AccountLog::class);
    }
}
