<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use App\Traits\MorphToApprover;
use Dcat\Admin\Admin;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Payment\Enums\WithdrawStatus;

class Withdraw extends Model
{
    use AutoCreateOrderNo,
        BelongsToUser,
        MorphToApprover,
        SoftDeletes;

    protected $table = 'payment_withdraws';

    protected $casts = [
        'status'  => WithdrawStatus::class,
        'source'  => 'json',
        'paid_at' => 'datetime',
    ];

    protected static function boot(): void
    {
        parent::boot();
        self::creating(function (Withdraw $withdraw) {
            $withdraw->tax    = bcdiv(bcmul($withdraw->amount, $withdraw->channel->rate ?? 0, 2), 100, 2);
            $withdraw->status = WithdrawStatus::INIT;
        });
    }

    public function channel(): BelongsTo
    {
        return $this->belongsTo(WithdrawChannel::class);
    }

    /**
     * Notes   : 拒绝提现，要退回暂扣的余额
     *
     * @Date   : 2023/10/30 14:14
     * <AUTHOR> <Jason.C>
     * @param  string|null  $reason
     * @return bool
     * @throws \Modules\Payment\Exceptions\RuleException
     */
    public function reject(?string $reason = null): bool
    {
        $this->user->account->exec(
            'withdraw_balance_reject',
            $this->amount,
            null,
            [
                'type' => 'withdraw_reject',
                'no'   => $this->no,
            ]);

        $this->approver = Admin::user();
        $this->status   = WithdrawStatus::REJECT;
        $this->reason   = $reason;
        return $this->save();
    }

    /**
     * Notes   : 提现申请通过
     *
     * @Date   : 2023/10/30 14:18
     * <AUTHOR> <Jason.C>
     * @param  string|null  $certificate
     * @return bool
     * @throws \Exception
     */
    public function pass(?string $certificate = null): bool
    {
        if (! $this->channel) {
            throw new Exception('提现渠道不可用');
        }
        if ($this->channel->getFactory()->disburse($this->no, bcsub($this->amount, $this->tax, 2), $this->source)) {
            $this->approver    = Admin::user();
            $this->status      = WithdrawStatus::PASS;
            $this->certificate = $certificate;
            $this->take        = bcsub($this->amount, $this->tax, 2);
            $this->paid_at     = now();
            return $this->save();
        } else {
            return false;
        }
    }
}
