<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\HasEasyStatus;
use Exception;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Payment\Enums\AccountType;

class AccountRule extends Model
{
    use Cachable,
        HasEasyStatus,
        SoftDeletes;

    protected $table = 'payment_account_rules';

    protected $casts = [
        'type'      => AccountType::class,
        'is_frozen' => 'boolean',
        'status'    => 'boolean',
        'is_sys'    => 'boolean',
    ];

    protected static function boot(): void
    {
        parent::boot();

        self::deleting(function ($model) {
            if ($model->is_sys) {
                throw new Exception('内置规则不允许删除');
            }
        });
    }
}
