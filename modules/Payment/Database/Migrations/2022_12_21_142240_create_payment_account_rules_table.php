<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\AccountType;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_account_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name')
                ->comment('规则名称');
            $table->string('slug')
                ->unique()
                ->comment('调用标记');
            $table->enum('type', AccountType::values())
                ->index()
                ->comment('变动账户类型');
            $table->integer('trigger')
                ->default(0)
                ->comment('触发次数');
            $table->decimal('variable', 20)
                ->default(0)
                ->comment('固定变动金额');
            $table->boolean('is_frozen')
                ->default(false);
            $table->boolean('status')
                ->default(false);
            $table->unsignedInteger('frozen_time')
                ->default(0)
                ->comment('冻结时长（秒）');
            $table->unsignedInteger('expire_time')
                ->default(0)
                ->comment('过期时间（秒）');
            $table->string('remark')
                ->nullable();
            $table->boolean('is_sys')
                ->default(0)
                ->comment('是否是系统内置策略，内置策略不允许删除');

            $table->timestamps();
            $table->softDeletes()
                ->index();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_account_rules');
    }
};
