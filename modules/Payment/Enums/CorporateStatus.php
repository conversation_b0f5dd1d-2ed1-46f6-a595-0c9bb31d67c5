<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

/**
 * Class CorporateStatus
 * 线下打款凭证状态
 *
 * @Author: 玄尘
 * @Date: 2023/9/8 9:45
 * @package Modules\Payment\Enums
 */
enum CorporateStatus: string
{
    use EnumMethods;

    case INIT   = 'init';
    case PASS   = 'pass';
    case REFUSE = 'refuse';

    const STATUS_MAP = [
        self::INIT->value   => '待审核',
        self::PASS->value   => '通过',
        self::REFUSE->value => '拒绝',
    ];

    const STATUS_AUDIT_MAP = [
        self::PASS->value   => '通过',
        self::REFUSE->value => '拒绝',
    ];

    const STATUS_LABEL = [
        self::INIT->value   => 'default',
        self::PASS->value   => 'success',
        self::REFUSE->value => 'warning',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}
