<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

enum BalancePayStatus: string
{
    use EnumMethods;

    case UNPAY = 'unpay';
    case PAID  = 'paid';

    const STATUS_MAP = [
        self::UNPAY->value => '未支付',
        self::PAID->value  => '已支付',
    ];

    const STATUS_LABEL = [
        self::UNPAY->value => 'warning',
        self::PAID->value  => 'success',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}