<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

enum WithdrawStatus: string
{
    use EnumMethods;

    case INIT   = 'init';
    case PASS   = 'pass';
    case REJECT = 'reject';

    const STATUS_MAP = [
        self::INIT->value   => '待审核',
        self::PASS->value   => '通过',
        self::REJECT->value => '拒绝',
    ];

    const AUDIT_MAP = [
        self::PASS->value   => '通过',
        self::REJECT->value => '拒绝',
    ];

    const STATUS_LABEL = [
        self::INIT->value   => 'info',
        self::PASS->value   => 'success',
        self::REJECT->value => 'danger',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}
