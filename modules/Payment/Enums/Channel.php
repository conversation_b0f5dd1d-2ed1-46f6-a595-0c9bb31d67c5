<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

enum Channel: string
{
    use EnumMethods;

    case ALIPAY_WEB  = 'alipay_web';
    case ALIPAY_WAP  = 'alipay_wap';
    case ALIPAY_APP  = 'alipay_app';
    case ALIPAY_POS  = 'alipay_pos';
    case ALIPAY_SCAN = 'alipay_scan';
    case ALIPAY_MINI = 'alipay_mini';

    case BALANCE_BALANCE = 'balance_balance';
    case BALANCE_SCORE   = 'balance_score';
    case BALANCE_COINS   = 'balance_coins';
    case BALANCE_CASH    = 'balance_cash';
    case BALANCE_OTHER   = 'balance_other';

    case CHINA_UMS_WEB = 'china_ums_web';
    case CHINA_UMS_WAP = 'china_ums_wap';

    case JD_QUICK_WEB = 'jd_quick_web';
    case JD_QUICK_WAP = 'jd_quick_wap';

    case UNIPAY_WEB  = 'unipay_web';
    case UNIPAY_WAP  = 'unipay_wap';
    case UNIPAY_SCAN = 'unipay_scan';
    case UNIPAY_POS  = 'unipay_pos';

    case WECHAT_MP   = 'wechat_mp';
    case WECHAT_WAP  = 'wechat_wap';
    case WECHAT_APP  = 'wechat_app';
    case WECHAT_SCAN = 'wechat_scan';
    case WECHAT_MINI = 'wechat_mini';

    case DOU_GONG_WECHAT = 'dou_gong_wechat';
    case DOU_GONG_ALIPAY = 'dou_gong_alipay';

    case LAKALA_CCSS        = 'lakala_ccss';
    case LAKALA_SCAN_ALIPAY = 'lakala_scan_alipay';
    case LAKALA_SCAN_WECHAT = 'lakala_scan_wechat';

    case TZ_BANK_WECHAT = 'tz_bank_wechat';
    case TZ_BANK_ALIPAY = 'tz_bank_alipay';

    case CORPORATE_BANK = 'corporate_bank';

    case PAYPAL_WEB = 'paypal_web';

    case APPLE_IAP = 'apple_iap';
    case SYSTEM    = 'system';

    const CHANNEL_DRIVER = [
        self::ALIPAY_WEB->value  => 'web',
        self::ALIPAY_WAP->value  => 'wap',
        self::ALIPAY_APP->value  => 'app',
        self::ALIPAY_POS->value  => 'pos',
        self::ALIPAY_SCAN->value => 'scan',
        self::ALIPAY_MINI->value => 'mini',

        self::BALANCE_BALANCE->value => 'balance',
        self::BALANCE_SCORE->value   => 'score',
        self::BALANCE_COINS->value   => 'coins',
        self::BALANCE_CASH->value    => 'cash',
        self::BALANCE_OTHER->value   => 'other',

        self::CHINA_UMS_WEB->value => 'web',
        self::CHINA_UMS_WAP->value => 'wap',

        self::JD_QUICK_WEB->value => 'web',
        self::JD_QUICK_WAP->value => 'wap',

        self::UNIPAY_WEB->value  => 'web',
        self::UNIPAY_WAP->value  => 'wap',
        self::UNIPAY_SCAN->value => 'scan',
        self::UNIPAY_POS->value  => 'pos',

        self::WECHAT_MP->value   => 'mp',
        self::WECHAT_WAP->value  => 'wap',
        self::WECHAT_APP->value  => 'app',
        self::WECHAT_SCAN->value => 'scan',
        self::WECHAT_MINI->value => 'mini',

        self::DOU_GONG_WECHAT->value => 'wechat',
        self::DOU_GONG_ALIPAY->value => 'alipay',

        self::LAKALA_CCSS->value        => 'ccss',
        self::LAKALA_SCAN_ALIPAY->value => 'scan_alipay',
        self::LAKALA_SCAN_WECHAT->value => 'scan_wechat',

        self::TZ_BANK_WECHAT->value => 'wechat',
        self::TZ_BANK_ALIPAY->value => 'alipay',

        self::CORPORATE_BANK->value => 'bank',

        self::PAYPAL_WEB->value => 'web',

        self::APPLE_IAP->value => 'apple_iap',
        self::SYSTEM->value    => 'system',
    ];

    const CHANNEL_MAP = [
        self::ALIPAY_WEB->value  => '电脑支付',
        self::ALIPAY_WAP->value  => '手机网站支付',
        self::ALIPAY_APP->value  => 'APP支付',
        self::ALIPAY_POS->value  => '刷卡支付',
        self::ALIPAY_SCAN->value => '扫码支付',
        self::ALIPAY_MINI->value => '小程序支付',

        self::BALANCE_BALANCE->value => '余额',
        self::BALANCE_SCORE->value   => '积分',
        self::BALANCE_COINS->value   => '代币',
        self::BALANCE_CASH->value    => '现金',
        self::BALANCE_OTHER->value   => '其他',

        self::CHINA_UMS_WEB->value => '电脑支付',
        self::CHINA_UMS_WAP->value => '手机网站支付',

        self::JD_QUICK_WEB->value => '电脑支付',
        self::JD_QUICK_WAP->value => '手机网站支付',

        self::UNIPAY_WEB->value  => '电脑支付',
        self::UNIPAY_WAP->value  => '手机网站支付',
        self::UNIPAY_SCAN->value => '扫码支付',
        self::UNIPAY_POS->value  => '刷卡支付',

        self::WECHAT_MP->value   => '公众号支付',
        self::WECHAT_WAP->value  => '手机网站支付',
        self::WECHAT_APP->value  => 'APP支付',
        self::WECHAT_SCAN->value => '扫码支付',
        self::WECHAT_MINI->value => '小程序支付',

        self::DOU_GONG_WECHAT->value => '斗拱微信',
        self::DOU_GONG_ALIPAY->value => '斗拱支付宝',

        self::LAKALA_CCSS->value        => '收银台',
        self::LAKALA_SCAN_ALIPAY->value => '直连扫码(支付宝)',
        self::LAKALA_SCAN_WECHAT->value => '直连扫码(微信)',

        self::TZ_BANK_WECHAT->value => '微信',
        self::TZ_BANK_ALIPAY->value => '支付宝',

        self::CORPORATE_BANK->value => '对公银行卡',

        self::PAYPAL_WEB->value => 'PAYPAL',

        self::APPLE_IAP->value => '苹果内购',
        self::SYSTEM->value    => '后台',
    ];

    const CHANNEL_LABEL = [
        self::ALIPAY_WEB->value  => 'info',
        self::ALIPAY_WAP->value  => 'info',
        self::ALIPAY_APP->value  => 'info',
        self::ALIPAY_POS->value  => 'info',
        self::ALIPAY_SCAN->value => 'info',
        self::ALIPAY_MINI->value => 'info',

        self::BALANCE_BALANCE->value => 'pink',
        self::BALANCE_SCORE->value   => 'pink',
        self::BALANCE_COINS->value   => 'pink',
        self::BALANCE_CASH->value    => 'pink',
        self::BALANCE_OTHER->value   => 'pink',

        self::CHINA_UMS_WEB->value => 'warning',
        self::CHINA_UMS_WAP->value => 'warning',

        self::JD_QUICK_WEB->value => 'red',
        self::JD_QUICK_WAP->value => 'red',

        self::UNIPAY_WEB->value  => 'danger',
        self::UNIPAY_WAP->value  => 'danger',
        self::UNIPAY_SCAN->value => 'danger',
        self::UNIPAY_POS->value  => 'danger',

        self::WECHAT_MP->value   => 'success',
        self::WECHAT_WAP->value  => 'success',
        self::WECHAT_APP->value  => 'success',
        self::WECHAT_SCAN->value => 'success',
        self::WECHAT_MINI->value => 'success',

        self::DOU_GONG_WECHAT->value => 'cyan',
        self::DOU_GONG_ALIPAY->value => 'cyan',

        self::LAKALA_CCSS->value        => 'blue-darker',
        self::LAKALA_SCAN_ALIPAY->value => 'blue-darker',
        self::LAKALA_SCAN_WECHAT->value => 'blue-darker',

        self::TZ_BANK_WECHAT->value => 'success',
        self::TZ_BANK_ALIPAY->value => 'info',

        self::CORPORATE_BANK->value => 'info',

        self::PAYPAL_WEB->value => 'blue',

        self::APPLE_IAP->value => 'indigo',
    ];

    public function toString(): string
    {
        return self::CHANNEL_MAP[$this->value];
    }

    public function toDriver(): string
    {
        return self::CHANNEL_DRIVER[$this->value];
    }
}