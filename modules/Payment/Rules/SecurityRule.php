<?php

namespace Modules\Payment\Rules;

use App\Facades\Api;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Hash;

class SecurityRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = Api::user();
        if (! $user->security->password) {
            $fail('未设置支付密码');
        }

        if (! Hash::check($value, $user->security->password)) {
            $fail('支付密码不正确');
        }
    }
}
