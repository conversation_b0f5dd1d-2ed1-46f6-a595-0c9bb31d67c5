<?php

namespace Modules\Payment\Jobs;

use Modules\Payment\Models\AccountLog;
use Modules\Payment\Models\AccountRule;

class AccountExpired extends PaymentBaseJob
{
    public function __construct(protected AccountLog $log)
    {
    }

    /**
     * Notes   : 处理过期积分
     *
     * @Date   : 2023/7/10 15:35
     * <AUTHOR> <Jason.C>
     * @throws \Modules\Payment\Exceptions\RuleException
     */
    public function handle(): void
    {
        $log = $this->log;

        if (! $log->is_expired && $log->expired_at) {
            $sum = AccountLog::where('account_id', $log->account_id)
                ->where('type', $log->type)
                ->where('amount', '>', 0)
                ->where('is_expired', false)
                ->sum('amount');

            $balance = $log->account->{$log->type->value};

            if ($sum >= $balance) {
                $expired = $log->amount - ($sum - $balance);

                $log->is_expired = true;
                $log->save();

                $rule = AccountRule::where('slug', 'expire_'.$log->type->value)->first();
                if ($rule) {
                    $log->account->exec($rule, -$expired, null, ['expired_id' => $log->id]);
                } else {
                    $log = $log->account->logs()->create([
                        'rule_id' => $rule->getKey(),
                        'type'    => $rule->type,
                        'amount'  => -$expired,
                        'balance' => $this->{$rule->type->value},
                        'source'  => [
                            'expired_id' => $log->id,
                        ],
                    ]);

                    $log->account->{$log->type->value} -= $expired;
                    $log->account->save();
                }
            }
        }
    }
}