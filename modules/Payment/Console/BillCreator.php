<?php

namespace Modules\Payment\Console;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Modules\Payment\Enums\BillType;
use Modules\Payment\Models\Bill;
use Modules\Payment\Models\Payment;
use Modules\Payment\Models\Refund;

class BillCreator extends Command
{
    protected $signature = 'bill:create {type}';

    protected $description = 'Create payment bill.';

    public function handle(): void
    {
        $type = $this->argument('type');

        if (method_exists($this, $type)) {
            call_user_func([$this, $type]);
        }
    }

    public function day(): void
    {
        $received = Payment::whereDate('paid_at', Carbon::yesterday())->sum('amount');
        $refund   = Refund::whereDate('refund_at', Carbon::yesterday())->sum('amount');
        Bill::create([
            'type'     => BillType::DAY->value,
            'date'     => Carbon::yesterday(),
            'received' => $received,
            'refund'   => $refund,
        ]);
    }

    public function week(): void
    {
        $week = [
            Carbon::now()->subWeek()->startOfWeek(),
            Carbon::now()->subWeek()->endOfWeek(),
        ];

        $received = Payment::whereBetween('paid_at', $week)->sum('amount');
        $refund   = Refund::whereBetween('refund_at', $week)->sum('amount');
        Bill::create([
            'type'     => BillType::WEEK->value,
            'date'     => Carbon::now()->subWeek()->endOfWeek(),
            'received' => $received,
            'refund'   => $refund,
        ]);
    }

    public function month(): void
    {
        $month = [
            Carbon::now()->subMonth()->startOfMonth(),
            Carbon::now()->subMonth()->endOfMonth(),
        ];

        $received = Payment::whereBetween('paid_at', $month)->sum('amount');
        $refund   = Refund::whereBetween('refund_at', $month)->sum('amount');
        Bill::create([
            'type'     => BillType::MONTH->value,
            'date'     => Carbon::now()->subMonth()->endOfMonth(),
            'received' => $received,
            'refund'   => $refund,
        ]);
    }

    public function quarter(): void
    {
        $quarter = [
            Carbon::now()->subQuarter()->startOfQuarter(),
            Carbon::now()->subQuarter()->endOfQuarter(),
        ];

        $received = Payment::whereBetween('paid_at', $quarter)->sum('amount');
        $refund   = Refund::whereBetween('refund_at', $quarter)->sum('amount');
        Bill::create([
            'type'     => BillType::QUARTER->value,
            'date'     => Carbon::now()->subQuarter()->endOfQuarter(),
            'received' => $received,
            'refund'   => $refund,
        ]);
    }

    public function year(): void
    {
        $received = Payment::whereYear('paid_at', Carbon::now()->subYear())->sum('amount');
        $refund   = Refund::whereYear('refund_at', Carbon::now()->subYear())->sum('amount');
        Bill::create([
            'type'     => BillType::YEAR->value,
            'date'     => Carbon::now()->subYear()->endOfYear(),
            'received' => $received,
            'refund'   => $refund,
        ]);
    }
}