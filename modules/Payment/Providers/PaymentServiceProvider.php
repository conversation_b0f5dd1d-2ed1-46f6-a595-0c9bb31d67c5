<?php

namespace Modules\Payment\Providers;

use App\Models\Configuration;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;
use Modules\Payment\Console\BillCreator;
use Modules\Payment\Console\CheckAccountExpired;
use Modules\Payment\Console\InitUserAccount;
use Modules\Payment\Models\Account;
use Modules\Payment\Models\Payment;
use Modules\Payment\Models\Recharge;
use Modules\Payment\Models\Security;

class PaymentServiceProvider extends ServiceProvider
{
    protected string $moduleName = 'Payment';

    public function boot(): void
    {
        $this->bootRelations();
    }

    public function register(): void
    {
        $this->registerConfig();

        $this->commands([
            CheckAccountExpired::class,
            InitUserAccount::class,
            BillCreator::class,
        ]);
    }

    protected function registerConfig(): void
    {
        Configuration::registerModuleConfig($this->moduleName);

        Config::set('filesystems.disks', array_merge(Config::get('filesystems.disks'), [
            'certs' => [
                'driver' => 'local',
                'root'   => storage_path('certs'),
                'throw'  => false,
            ],
        ]));

        Config::set('horizon.defaults.payment', [
            'connection'          => 'redis',
            'queue'               => ['payment'],
            'balance'             => 'auto',
            'autoScalingStrategy' => 'time',
            'maxProcesses'        => 1,
            'tries'               => 1,
        ]);

        Config::set('logging.channels.payment', [
            'driver' => 'daily',
            'path'   => storage_path('logs/payments/log.log'),
            'level'  => config('logging.channels.daily.level'),
            'days'   => 0,
        ]);
    }

    public function provides(): array
    {
        return ['pay.alipay', 'pay.wechat', 'pay.unipay'];
    }

    private function bootRelations(): void
    {
        User::resolveRelationUsing('payments', fn(User $user) => $user->hasMany(Payment::class));
        User::resolveRelationUsing('security', fn(User $user) => $user->hasOne(Security::class));
        User::resolveRelationUsing('account', fn(User $user) => $user->hasOne(Account::class));

        Relation::morphMap([
            'recharge' => Recharge::class,
        ]);
    }
}
