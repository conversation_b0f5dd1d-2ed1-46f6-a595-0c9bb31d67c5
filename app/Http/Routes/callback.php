<?php

use App\Http\Controllers\Api\CallBack\IndexController;
use App\Http\Controllers\Api\CallBack\VolcengineFunctionCallController;
use App\Http\Controllers\Api\CallBack\XfYunCkmController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    $router->any('callback/ai_agent_call', [IndexController::class, 'aiAgentCall']);
    $router->any('function/call', [VolcengineFunctionCallController::class, 'index']);
    $router->any('callback/xfyun_ckm', [XfYunCkmController::class, 'index']);
});
