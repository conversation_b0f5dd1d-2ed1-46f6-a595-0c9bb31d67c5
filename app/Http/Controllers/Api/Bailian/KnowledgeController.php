<?php

namespace App\Http\Controllers\Api\Bailian;

use App\Enums\Bailian\BailianLevelEnum;
use App\Enums\Bailian\MemberStatusEnum;
use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Bailian\Knowledge\AddKnowledgeRequest;
use App\Http\Requests\Bailian\Knowledge\DeleteKnowledgeRequest;
use App\Http\Requests\Bailian\Knowledge\DetailKnowledgeRequest;
use App\Http\Requests\Bailian\Knowledge\UpdateKnowledgeRequest;
use App\Http\Resources\Bailian\Knowledge\KnowledgeCollection;
use App\Http\Resources\Bailian\Knowledge\KnowledgeNoAuthResource;
use App\Http\Resources\Bailian\Knowledge\KnowledgeResource;
use App\Jobs\BaiLian\CreateKnowledgeIndexJob;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeMember;
use App\Models\SystemConfig;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Validator;

class KnowledgeController extends ApiController
{

    public function before(Request $request)
    {
        $user                             = $request->kernel->user();
        $maxBailianKnowledgeBasesForUser  = SystemConfig::getValue('max_bailian_knowledge_bases_for_user', 3);
        $singleKnowledgeBaseCapacityGb    = SystemConfig::getValue('single_knowledge_base_capacity_gb', 10);
        $maxAllKnowledgeBaseCapacityGb    = $maxBailianKnowledgeBasesForUser * $singleKnowledgeBaseCapacityGb;
        $knowledge_all_size               = BailianKnowledge::where('user_id', $user->id)->sum('size') ?? 0;
        $max_bailian_knowledge_chat_count = SystemConfig::getValue('max_bailian_knowledge_chat_count', 5);
        $data                             = [
            'configuration' => [
                'max_bailian_knowledge_bases_for_user' => $maxBailianKnowledgeBasesForUser,
                'single_knowledge_base_capacity_gb'    => $singleKnowledgeBaseCapacityGb,
                'max_all_knowledge_base_capacity_gb'   => $maxAllKnowledgeBaseCapacityGb,
                'max_bailian_knowledge_chat_count'     => $max_bailian_knowledge_chat_count,
            ],
            'current'       => [
                'knowledge_count'    => BailianKnowledge::where('user_id', $user->id)->count(),
                'knowledge_all_size' => format_byte($knowledge_all_size)
            ]

        ];
        return $request->kernel->success($data);
    }

    /**
     * 获取知识库列表
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $name     = $request->name ?? '';
        $type     = $request->type ?? 'my';// my  public  featured index
        $user     = $request->kernel->user();
        $pagesize = $request->pagesize ?? 10;

        $query = BailianKnowledge::query()
            ->ofType($type, $user)
            ->when($name, function ($query) use ($name) {
                return $query->where('name', 'like', "%{$name}%");
            })
            ->with(['categories'])
            ->withCount('items')
            ->latest();

        $knowledge = $query->paginate($pagesize);

        return $request->kernel->success(new KnowledgeCollection($knowledge));
    }

    /**
     * Notes: 创建知识库
     *
     * @Author: 玄尘
     * @Date: 2025/5/7 18:21
     * @param  \App\Http\Requests\Bailian\Knowledge\AddKnowledgeRequest  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Throwable
     */
    public function create(AddKnowledgeRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user        = $request->kernel->user();
            $name        = $request->input('name');
            $description = $request->input('description', '');
            $sourceType  = $request->input('source_type', BailianKnowledge::SOURCE_TYPE_CATEGORY);
            $level       = $request->input('level', BailianLevelEnum::PRIVATE);
            $autoJoin    = $request->input('auto_join', 0);
            // 创建知识库
            $maxBailianKnowledgeBasesForUser = SystemConfig::getValue('max_bailian_knowledge_bases_for_user', 5);
            $knowledgeCount                  = BailianKnowledge::where('user_id', $user->id)->count();
            if ($knowledgeCount >= $maxBailianKnowledgeBasesForUser) {
                throw new ValidatorException('知识库数量已达上限');
            }
            $data = [
                'user_id'        => $user->id,
                'name'           => $name,
                'description'    => $description,
                'source_type'    => $sourceType,
                'structure_type' => BailianKnowledge::STRUCTURE_TYPE_UNSTRUCTURED,
                'sink_type'      => BailianKnowledge::SINK_TYPE_BUILT_IN,
                'level'          => BailianLevelEnum::PRIVATE,
                'status'         => 1,
            ];
            if ($level) {
                $data['level'] = $level;
            }
            if ($autoJoin) {
                $data['auto_join'] = $autoJoin;
            }

            $knowledge = BailianKnowledge::create($data);

            // 如果是分类类型，关联分类
            if ($sourceType === BailianKnowledge::SOURCE_TYPE_CATEGORY) {
                $category = $user->getDefaultCategory();
                $knowledge->categories()->attach($category->id);
            }
            // 创建知识库成员记录
            $knowledge->members()->create([
                'user_id'  => $user->id,
                'position' => BailianKnowledgeMember::POSITION_OWNER,
                'status'   => MemberStatusEnum::APPROVED,
            ]);
            DB::commit();

            return $request->kernel->success(new KnowledgeResource($knowledge));
        } catch (\Exception $e) {
            DB::rollBack();

            return $request->kernel->error('创建知识库失败：'.$e->getMessage());
        }
    }

    public function detail(DetailKnowledgeRequest $request): JsonResponse
    {
        $knowledgeId = $request->input('knowledge_id');
        $is_web      = $request->input('is_web', false);

        $knowledge = BailianKnowledge::with(['categories'])
            ->where('id', $knowledgeId)
            ->first();
        if (! $knowledge) {
            throw new ValidatorException('知识库不存在');
        }
        $user = $request->kernel->user();
        if (! $knowledge->canView($user) && ! $is_web) {
            throw new ValidatorException('您没有权限访问该知识库');
        }

        return $request->kernel->success(new KnowledgeResource($knowledge));
    }

    public function detailNoAuth(BailianKnowledge $knowledge)
    {
        $knowledge->load(['categories']);
        return $this->success(new KnowledgeNoAuthResource($knowledge));
    }

    public function update(UpdateKnowledgeRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user        = $request->kernel->user();
            $knowledgeId = $request->input('knowledge_id');
            $name        = $request->input('name');
            $description = $request->input('description');
            $level       = $request->input('level', null);
            $autoJoin    = $request->input('auto_join', 0);

            $knowledge = BailianKnowledge::find($knowledgeId);
            if ($knowledge->user->isNot($user)) {
                throw new ValidatorException('您没有权限操作');
            }

            // 更新基本信息
            if ($name) {
                $knowledge->name = $name;
            }

            if ($description) {
                $knowledge->description = $description;
            }
            if ($level) {
                $knowledge->level = $level;
            }
            if ($autoJoin) {
                $knowledge->auto_join = $autoJoin;
            }

            $knowledge->save();

            DB::commit();

            // 如果知识库ID为空，重新创建索引
            if (empty($knowledge->knowledge_id)) {
                CreateKnowledgeIndexJob::dispatch($knowledge);
            }

            return $request->kernel->success(new KnowledgeResource($knowledge));
        } catch (\Exception $e) {
            DB::rollBack();

            return $request->kernel->error('更新知识库失败：'.$e->getMessage());
        }
    }

    public function delete(DeleteKnowledgeRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user        = $request->kernel->user();
            $knowledgeId = $request->input('knowledge_id');

            $knowledge = BailianKnowledge::find($knowledgeId);
            if (! $knowledge) {
                throw new ValidatorException('知识库不存在');
            }
            if ($knowledge->user->isNot($user)) {
                throw new ValidatorException('您没有权限');
            }

            $knowledge->delete();

            DB::commit();
            return $request->kernel->success('删除成功');
        } catch (\Exception $e) {
            DB::rollBack();

            return $request->kernel->error('删除知识库失败：'.$e->getMessage());
        }
    }

    /**
     * Notes: 设置权限
     *
     * @Author: 玄尘
     * @Date: 2025/5/8 17:22
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function level(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'knowledge_id' => 'required|integer|exists:bailian_knowledge,id',
            'level'        => 'required|in:'.BailianLevelEnum::valuesToString(),
            'auto_join'    => 'nullable|in:0,1'
        ], [
            'knowledge_id.required' => '知识库ID不能为空',
            'knowledge_id.exists'   => '知识库不存在',
            'level.required'        => '权限不能为空',
            'level.in'              => '权限值错误',
            'auto_join.in'          => '自动加入权限值错误',
        ]);

        if ($validator->fails()) {
            throw new ValidatorException($validator->errors()->first());
        }
        $knowledge_id = $request->input('knowledge_id');
        $level        = $request->input('level');
        $auto_join    = $request->input('auto_join', '');
        $user         = $request->kernel->user();
        $knowledge    = BailianKnowledge::find($knowledge_id);
        if ($knowledge->user->isNot($user)) {
            throw new ValidatorException('您没有权限操作');
        }
        $knowledge->level = $level;
        if (is_numeric($auto_join)) {
            $knowledge->auto_join = $auto_join;
        }
        $knowledge->save();
        return $request->kernel->success('设置成功');
    }

}
