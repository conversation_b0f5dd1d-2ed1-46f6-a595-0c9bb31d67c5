<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\OfflineTask\OfflineTaskKernel;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class OfflineTaskTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'offline_task';
    }

    public function description(): string
    {
        $string = "执行用户离线检测任务：\n\n";
        foreach (config('offline.task.tools', []) as $className) {
            $class  = new $className();
            $string .= "- ".$class->description()."\n\n\n";
        }
        return $string;
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'command'  => [
                    'type'        => 'string',
                    'description' => '监测命令',
                ],
                'params'   => [
                    'type'        => 'array',
                    'description' => '注入的参数',
                ],
                'wateauth' => [
                    'type'        => 'string',
                    'description' => '用户的鉴权信息,聊天是传入',
                ],
            ],
            'required'   => ['command', 'params', 'wateauth'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'OfflineTaskTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $user    = $this->getUser($arguments);
            $command = $arguments['command'];
            $params  = $arguments['params'];
            $kernel  = new OfflineTaskKernel($command);
            if ($kernel->hasTask()) {
                $task = $kernel->join($user, $params);
                return $this->success([
                    'task_id' => $task->id,
                    'command' => $task->command,
                    'minutes' => $task->minutes,
                ], '创建任务成功');
            } else {
                return $this->error('监听工具不存在');
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
