<?php

namespace App\OfflineTask\Tools;

use App\Models\OfflineTask;
use App\Models\User;
use App\OfflineTask\BaseClass\BaseTask;
use Exception;
use Modules\Payment\Models\AccountLog;

class AccountChargeTask extends BaseTask
{
    public string $name    = 'AccountCharge';
    public int    $minutes = 20;

    /**
     * 独立架构每个任务解析出任务的入参，可写可不写
     * 也可以通过BaseTask中的join加入任务（外部控制入参）
     *
     * @return array
     */
    public static function taskJoin(User $user): OfflineTask
    {
        $task = new static();
        $data = [
            'user_id' => $user->id,
            'command' => $task->name,
            'minutes' => $task->getMinutes(),
            'params'  => [
                'account'  => 'score',
                'type'     => 'out',
                'start_at' => now()->toDateTimeString(),
            ],
            'status'  => OfflineTask::STATUS_ING,
            'next_at' => now()->addMinutes($task->getMinutes()),
            'last_at' => null,
        ];
        return OfflineTask::create($data);
    }

    /**
     * 给予到MCP工具中的说明
     *
     * @return string
     */
    public function description(): string
    {
        return <<<EOF
            ```
            用户积分账户变动的监测
            command:$this->name
            params:
              - account:账户类型[in:score,balance],score:积分,balance:余额 
              - type:监测类型[in:in,out],in:收入,out:支出
            ```
        EOF;
    }

    /**
     * 任务执行过程
     *
     * @return void
     */
    public function run(): void
    {
        try {
            #Todo 任务执行过程
            $params  = $this->task->params;
            $account = $params['account'];
            $type    = $params['type'];
            $startAt = $params['start_at'] ?? $this->task->created_at->toDateTimeString();
            if (! $this->task->user) {
                $this->task->delete();
                throw new Exception('用户不存在');
            }
            $model                    = AccountLog::where('account_id', $this->task->user->account->id)
                ->where('type', $account)
                ->where('created_at', '>', $startAt);
            $model                    = match ($type) {
                'in' => $model->where('amount', '>', 0),
                'out' => $model->where('amount', '<', 0),
                default => 0,
            };
            $chargeAmount             = abs($model->sum('amount'));
            $this->params['start_at'] = now()->toDateTimeString();
            $this->success([
                'amount' => $chargeAmount,
            ]);
            if ($chargeAmount > 0) {
                $this->notification();
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->setLogs();
    }

}
