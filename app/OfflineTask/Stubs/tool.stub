<?php

namespace App\OfflineTask\Tools;

use App\OfflineTask\BaseClass\BaseTask;
use Exception;

class {{ className }} extends BaseTask
{
    public string $name = '{{ taskName }}';
    public int    $minutes = 20;//运行间隔

    /**
     * 给予到MCP工具中的说明
     *
     * @return string
     */
    public function description(): string
    {
        return '';
    }

    /**
     * 独立架构每个任务解析出任务的入参，可写可不写
     * 也可以通过BaseTask中的join加入任务（外部控制入参）
     *
     * @return array
     */
    public static function taskJoin(User $user): OfflineTask
    {

    }

    /**
     * 任务执行过程
     *
     * @return void
     */
    public function run(): void
    {
        try {
            #Todo 任务执行过程
            $this->success(['abc' => 'abc']);//记录成功数据
            if (true) {
                $this->notification();//执行通知
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->setLogs();//产生记录
    }

}
