<?php

namespace App\OfflineTask\Notifications;

use Mo<PERSON>les\Notification\Contracts\BaseNotification;

class {{ className }} extends BaseNotification
{
     /**
      * 固定传入
      *
      * @param  array  $params  任务执行时入参
      * @param  array  $result  任务完成后出参数
      */
     public function __construct(protected array $params, protected array $result)
     {
     }

     public function toDatabase(Model $notifiable): DatabaseMessage|array
     {
         return [
             'title'   => self::getTitle(),
             'content' => '通知内容',
         ];
     }

     public static function getTitle(): string
     {
         return '通知标题';
     }

     public function toIM(Model $notifiable): IMMessage|array
     {
         $imUser = $notifiable->imUser;
         if (! $imUser) {
             return [];
         }

         return new IMMessage(
             static::getTitle(),
            'IM通知内容',
             [$imUser->im_user_id]
         );
     }
}