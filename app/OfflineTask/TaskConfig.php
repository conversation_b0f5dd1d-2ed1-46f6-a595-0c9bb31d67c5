<?php

use App\OfflineTask\Tools\AccountChargeTask;

return [
    'enable'       => true,//是否开启
    'max_time'     => 10,//最大次数
    'can_next_day' => false,//是否允许到第二天
    'min_minutes'  => 5,//最小间隔时间（分）
    'max_minutes'  => 60,//最大间隔时间（分）
    'stubs'        => [
        'task'         => app_path('OfflineTask/Stubs/tool.stub'),
        'notification' => app_path('OfflineTask/Stubs/notification.stub'),
        'validation'   => app_path('OfflineTask/Stubs/validation.stub'),
    ],
    'tools'        => [
        AccountChargeTask::class,
    ],
];
