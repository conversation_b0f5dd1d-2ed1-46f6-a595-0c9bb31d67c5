<?php

namespace App\OfflineTask\Validations;

use App\OfflineTask\BaseClass\BaseValidation;
use Illuminate\Validation\Rule;
use Modules\Payment\Enums\AccountType;

class AccountChargeValidation extends BaseValidation
{

    protected function rules(): array
    {
        return [
            'account' => ['required', Rule::in(AccountType::values())],
            'type'    => ['required', Rule::in(['in', 'out'])],
        ];
    }

    protected function messages(): array
    {
        return [
            'account.required' => '请传入监控账户类型account',
            'account.in'       => '监控账户account类型不正确',
            'type.required'    => '请传入监控类型type',
            'type.in'          => '监控类型type不正确',
        ];
    }
}