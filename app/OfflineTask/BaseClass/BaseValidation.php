<?php

namespace App\OfflineTask\BaseClass;

use Exception;
use Illuminate\Support\Facades\Validator;

class BaseValidation
{
    /**
     * 固定传入
     *
     * @param  array  $params  任务执行时入参
     */
    public function __construct(protected array $params)
    {
        if (method_exists($this, 'rules')) {
            $messages   = method_exists($this, 'messages') ? $this->messages() : [];
            $validation = Validator::make($params, $this->rules(), $messages);
            if ($validation->errors()->count() > 0) {
                throw new Exception($validation->errors()->first());
            }
        }
    }
}