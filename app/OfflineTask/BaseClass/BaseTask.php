<?php

namespace App\OfflineTask\BaseClass;

use App\Models\OfflineTask;
use Exception;
use Illuminate\Support\Facades\DB;

class BaseTask
{
    protected array       $params  = [];
    protected OfflineTask $task;
    protected bool        $over    = false;
    protected int         $minutes = 5;
    protected array       $result  = [
        'status'  => false,
        'data'    => [],
        'message' => '',
    ];

    public function getMinutes(): int
    {
        return $this->minutes;
    }

    /**
     * 当前任务是否成功
     *
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->result['status'];
    }

    /**
     * 获取错误信息
     *
     * @return string
     */
    public function getMessage(): string
    {
        return $this->result['message'];
    }

    /**
     * 设置任务对象实例
     *
     * @param  \App\Models\OfflineTask  $task
     * @return $this
     */
    public function setTask(OfflineTask $task)
    {
        $this->task   = $task;
        $this->params = $task->params;
        return $this;
    }

    /**
     * 注册通知过程
     *
     * @param  string  $name
     * @return void
     */
    protected function notification(): void
    {
        $name      = $this->task->command;
        $className = "\\App\\OfflineTask\\Notifications\\".$name.'Notification';
        if (class_exists($className)) {
            $notification = new $className($this->params, $this->result);
            $this->task->user->notify($notification);
        } else {
            throw new Exception('未检测到类');
        }
    }

    /**
     * 成功
     *
     * @param  array  $data
     * @return void
     */
    protected function success(array $data, string $message = 'success')
    {
        $this->result['status']  = true;
        $this->result['data']    = $data;
        $this->result['message'] = $message;
    }

    /**
     * 失败
     *
     * @param  string  $message
     * @return void
     */
    protected function error(string $message)
    {
        $this->result['status']  = false;
        $this->result['message'] = $message;
    }

    /**
     * 设置记录与任务状态
     *
     * @return void
     * @throws \Throwable
     */
    protected function setLogs()
    {
        DB::transaction(function () {
            $log = $this->task->logs()->create([
                'run_params' => $this->params,
                'result'     => $this->result,
            ]);
            if ($log) {
                $count = $this->task->logs()->count();
                if ($count >= config('offline.task.max_time')) {
                    $this->over = true;
                }

                list($status, $nextAt) = match ($this->over) {
                    true => [OfflineTask::STATUS_OVER, null],
                    false => [OfflineTask::STATUS_ING, now()->addMinutes($this->task->minutes)],
                };
                $this->task->status  = $status;
                $this->task->next_at = $nextAt;
                $this->task->params  = $this->params;
                $this->task->last_at = now();
                $this->task->save();
            }
        });
    }
}