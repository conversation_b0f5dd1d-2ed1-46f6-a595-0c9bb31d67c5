<?php

namespace App\OfflineTask\Notifications;

use Illuminate\Database\Eloquent\Model;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\DatabaseMessage;
use Modules\Notification\Messages\IMMessage;
use Modules\Payment\Enums\AccountType;

class AccountChargeNotification extends BaseNotification
{
    /**
     * 固定传入
     *
     * @param  array  $params  任务执行时入参
     * @param  array  $result  任务完成后出参数
     */
    public function __construct(protected array $params, protected array $result)
    {
    }

    public function toDatabase(Model $notifiable): DatabaseMessage|array
    {
        $accountType = AccountType::ACCOUNT_TYPE_MAP[$this->params['account']];
        $prefix      = match ($this->params['type']) {
            'in' => '收入',
            'out' => '使用',
        };
        return [
            'title'   => self::getTitle(),
            'content' => sprintf(
                '您的账户「%s」%s了：「%s」',
                $accountType,
                $prefix,
                $this->result['data']['amount'],
            ),
        ];
    }

    public static function getTitle(): string
    {
        return '账户变动通知';
    }

    public function toIM(Model $notifiable): IMMessage|array
    {
        $imUser = $notifiable->imUser;
        if (! $imUser) {
            return [];
        }

        $accountType = AccountType::ACCOUNT_TYPE_MAP[$this->params['account']];

        $prefix = match ($this->params['type']) {
            'in' => '收入',
            'out' => '使用',
        };
        return new IMMessage(
            static::getTitle(),
            sprintf(
                '您的账户「%s」%s了：「%s」',
                $accountType,
                $prefix,
                $this->result['data']['amount'],
            ),
            [$imUser->im_user_id]
        );
    }
}