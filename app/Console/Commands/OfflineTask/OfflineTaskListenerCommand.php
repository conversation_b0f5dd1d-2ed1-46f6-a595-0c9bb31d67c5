<?php

namespace App\Console\Commands\OfflineTask;

use App\Jobs\OfflineTask\OfflineTaskRunJob;
use App\Models\OfflineTask;
use Illuminate\Console\Command;

class OfflineTaskListenerCommand extends Command
{
    protected $signature   = 'offline:task';
    protected $description = '离线任务监听任务执行';

    public function handle()
    {
        $tasks = OfflineTask::where('status', OfflineTask::STATUS_ING)
            ->where('next_at', '<=', now())
            ->get();
        foreach ($tasks as $task) {
            OfflineTaskRunJob::dispatch($task);
        }
    }
}