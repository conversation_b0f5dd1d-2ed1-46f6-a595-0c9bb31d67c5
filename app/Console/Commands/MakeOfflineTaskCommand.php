<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Support\Str;

class MakeOfflineTaskCommand extends Command
{
    protected $signature   = 'make:offline {name : 离线}';
    protected $description = '创建离线监听任务';

    /**
     * The filesystem instance.
     *
     * @var \Illuminate\Filesystem\Filesystem
     */
    protected $files;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(Filesystem $files)
    {
        parent::__construct();

        $this->files = $files;
    }

    public function handle()
    {
        $baseClassName     = $this->getClassName();
        $taskClass         = Str::finish($baseClassName, 'Task');
        $notificationClass = Str::finish($baseClassName, 'Notification');
        $validationClass   = Str::finish($baseClassName, 'Validation');
        $this->buildTask($taskClass, $baseClassName);
        $this->buildNotification($notificationClass);
        $this->buildValidation($validationClass);
        return 0;
    }

    /**
     * Get the class name from the command argument.
     *
     * @return string
     */
    protected function getClassName()
    {
        $name = $this->argument('name');

        // Clean up the input: remove multiple spaces, hyphens, underscores
        // and handle mixed case input
        $name = preg_replace('/[\s\-_]+/', ' ', trim($name));

        // Convert to StudlyCase
        $name = Str::studly($name);

        return $name;
    }

    protected function buildTask(string $className, string $baseClassName)
    {
        $path = $this->getPathTask($className);
        $this->info('---判断工具是否存在---');
        if ($this->files->exists($path)) {
            $this->error("文件 {$path} 已经存在!");
        } else {
            $this->makeDirectory($path);
            if ($taskStub = config('offline.task.stubs.task', '')) {
                $stub    = $this->files->get($taskStub);
                $content = $this->replaceStubPlaceholders($stub, $className, $baseClassName);
                $this->files->put($path, $content);
            }
            $this->info("工具 {$className} 创建成功!");
            $this->registerToolConfig($className);
        }
    }

    protected function getPathTask(string $fileName)
    {
        // Create the file in the app/MCP/Tools directory
        return app_path("OfflineTask/Tools/{$fileName}.php");
    }

    /**
     * Build the directory for the class if necessary.
     *
     * @param  string  $path
     * @return string
     */
    protected function makeDirectory($path)
    {
        $directory = dirname($path);

        if (! $this->files->isDirectory($directory)) {
            $this->files->makeDirectory($directory, 0755, true, true);
        }

        return $directory;
    }

    protected function replaceStubPlaceholders(string $stub, string $className, string $taskName = '')
    {
        return str_replace(
            ['{{ className }}', '{{ taskName }}'],
            [$className, $taskName],
            $stub
        );
    }

    protected function registerToolConfig(string $className)
    {
        $configPath = app_path('OfflineTask/TaskConfig.php');
        if (! file_exists($configPath)) {
            $this->error("找不到配置文件: {$configPath}");
            return false;
        }
        $content = file_get_contents($configPath);
        if (! preg_match('/[\'"]tools[\'"]\s*=>\s*\[(.*?)\s*\],/s', $content, $matches)) {
            $this->error('配置文件中找不到tools数组.');
            return false;
        }
        $toolsArrayContent = $matches[1];
        $fullEntry         = "\n        \\App\\OfflineTask\\Tools\\{$className}::class,";
        if (strpos($toolsArrayContent, $className) !== false) {
            $this->info('工具已在配置文件中注册.');
        }
        $newToolsArrayContent = $toolsArrayContent.$fullEntry;
        $newContent           = str_replace($toolsArrayContent, $newToolsArrayContent, $content);

        // Write the updated content back to the config file
        if (file_put_contents($configPath, $newContent)) {
            $this->info('工具配置成功');
        } else {
            $this->error('注册工具失败');
        }
    }

    protected function buildNotification(string $className)
    {
        $path = $this->getPathNotification($className);
        $this->info('---判断通知是否存在---');
        if ($this->files->exists($path)) {
            $this->error("文件 {$path} 已经存在!");
        } else {
            $this->makeDirectory($path);
            if ($taskStub = config('offline.task.stubs.notification', '')) {
                $stub    = $this->files->get($taskStub);
                $content = $this->replaceStubPlaceholders($stub, $className);
                $this->files->put($path, $content);
            }
            $this->info("通知 {$className} 创建成功!");
        }
    }

    protected function getPathNotification(string $fileName)
    {
        // Create the file in the app/MCP/Tools directory
        return app_path("OfflineTask/Notifications/{$fileName}.php");
    }

    protected function buildValidation(string $className)
    {
        $path = $this->getPathValidation($className);
        $this->info('---判断表单验证是否存在---');
        if ($this->files->exists($path)) {
            $this->error("文件 {$path} 已经存在!");
        } else {
            $this->makeDirectory($path);
            if ($taskStub = config('offline.task.stubs.validation', '')) {
                $stub    = $this->files->get($taskStub);
                $content = $this->replaceStubPlaceholders($stub, $className);
                $this->files->put($path, $content);
            }
            $this->info("验证表单 {$className} 创建成功!");
        }
    }

    protected function getPathValidation(string $fileName)
    {
        // Create the file in the app/MCP/Tools directory
        return app_path("OfflineTask/Validations/{$fileName}.php");
    }
}