<?php

namespace App\Admin\Controllers\Bailian;

use App\Admin\Traits\WithUploads;
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Models\BailianArticle;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class ArticleController extends AdminController
{
    use WithUploads;

    protected string $title = '笔记列表';

    public function grid(): Grid
    {
        return Grid::make(BailianArticle::class, function (Grid $grid) {
            $grid->model()
                ->with(['user', 'knowledgeItem.baiLianKnowledge'])
                ->latest('id');
            $grid->showBatchDelete();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '标题');
                $filter->like('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
            });

            $grid->column('id', '#ID#');
            $grid->column('title', '标题');
            $grid->column('type', '类型')->display(function () {
                return $this->type->toString();
            });
//            $grid->column('content', '内容预览')
//                ->display(function () {
//                    $content = $this->content;
//                    return match ($this->type->value) {
//                        BailianArticleTypeEnum::ARTICLE->value => Str::limit($content['data'] ?? '', 100),
//                        BailianArticleTypeEnum::IMAGE->value => '<img src="'.($content['image'] ?? '').'" style="max-width:100px;max-height:100px;" alt="预览图">',
//                        BailianArticleTypeEnum::RECORD->value => '音频时长: '.($content['audioDuration'] ?? 0).'秒',
//                        BailianArticleTypeEnum::LINK->value => '<a href="'.($content['link'] ?? '').'" target="_blank" rel="noopener">'.($content['linkTitle'] ?? '').'</a>',
//                        default => '',
//                    };
//                });
            $grid->column('knowledge', '知识库')
                ->display(function () {
                    return $this->knowledgeItem->bailianKnowledge->name ?? '---';
                });
            $grid->column('username', '所属用户')
                ->display(fn() => $this->user->show_name);
            $grid->column('size', '大小')
                ->display(fn() => $this->readable_size);
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(BailianArticle::class, function (Form $form) {
            $form->text('title', '标题')->required();
            $form->select('user_id', '隶属用户')
                ->options(function ($userId) {
                    if ($userId) {
                        $user = User::find($userId);
                        return $user ? [$userId => $user->show_name] : [];
                    }
                    return [];
                })
                ->ajax(route('admin.user.users.ajax'))
                ->required();

            $form->select('type', '笔记类型')
                ->options(BailianArticleTypeEnum::STATUS_MAP)
                ->required()
                ->when(BailianArticleTypeEnum::RECORD->value, function (Form $form) {
                    $form->text('content.audio', '音频');
                    $form->number('content.audioDuration', '音频时长(秒)');
                })
                ->when(BailianArticleTypeEnum::MEETING->value, function (Form $form) {
                    $form->text('content.audio', '音频');
                    $form->number('content.audioDuration', '音频时长(秒)');
                    $form->textarea('content.text_record', '转录数据')->help('JSON格式的转录数据');
                })
                ->when(BailianArticleTypeEnum::LINK->value, function (Form $form) {
                    $form->url('content.link', '链接地址');
                    $form->text('content.linkTitle', '链接标题');
                    $form->image('content.linkImage', '链接图标');
                });
            $form->text('content.image', '图片');
            $form->markdown('content.data', '内容详情');

            $form->saving(function (Form $form) {
                $type    = $form->type;
                $input   = $form->input(); // 获取所有输入数据
                $content = BailianArticle::getContentDataByInput($type, $input);

                $form->content = $content;
            });
        });
    }

    /**
     * 根据类型构建内容数据
     */
    private function buildContentByType(string $type, Form $form): array
    {
        switch ($type) {
            case BailianArticleTypeEnum::ARTICLE->value:
                return ['data' => $form->input('content.data')];

            case BailianArticleTypeEnum::IMAGE->value:
                return [
                    'image' => $form->input('content.image'),
                    'data'  => $form->details
                ];

            case BailianArticleTypeEnum::RECORD->value:
                return [
                    'audio'         => $form->input('content.audio'),
                    'audioDuration' => (int) $form->input('content.audioDuration', 0),
                    'data'          => $form->input('content.data')
                ];

            case BailianArticleTypeEnum::LINK->value:
                return [
                    'link'      => $form->input('content.link'),
                    'linkTitle' => $form->input('content.linkTitle'),
                    'linkImage' => $form->input('content.linkImage'),
                    'data'      => $form->input('content.data')
                ];

            default:
                return [];
        }
    }
}