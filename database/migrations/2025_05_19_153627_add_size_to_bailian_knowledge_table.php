<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_knowledge', function (Blueprint $table) {
            $table->bigInteger('size')->default(0)->comment('知识库大小(字节)')->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_knowledge', function (Blueprint $table) {
            $table->dropColumn('size');
        });
    }
};
