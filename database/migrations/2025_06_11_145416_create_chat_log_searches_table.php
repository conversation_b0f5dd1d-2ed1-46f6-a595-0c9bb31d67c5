<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_log_searches', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('chat_log_id');
            $table->unsignedInteger('log_index');
            $table->string('site')->nullable();
            $table->string('icon')->nullable();
            $table->string('title')->nullable();
            $table->text('remark')->nullable();
            $table->string('url')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_log_searches');
    }
};
